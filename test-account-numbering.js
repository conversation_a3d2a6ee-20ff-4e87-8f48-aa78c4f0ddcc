/**
 * اختبار منطق ترقيم الحسابات
 * هذا الملف لاختبار صحة منطق الترقيم قبل التطبيق
 */

// محاكاة منطق ترقيم الحسابات الرئيسية
function testRootAccountNumbering() {
  console.log('🧪 اختبار ترقيم الحسابات الرئيسية...\n');

  const rootTypeNumbers = {
    'Asset': '1000',
    'Liability': '2000', 
    'Equity': '3000',
    'Income': '4000',
    'Expense': '5000',
  };

  // اختبار الحساب الأول من كل نوع
  Object.entries(rootTypeNumbers).forEach(([type, baseNumber]) => {
    console.log(`✅ ${type}: الحساب الأول -> ${baseNumber}`);
  });

  console.log('\n🔢 اختبار الحسابات التالية:');
  
  // محاكاة وجود حسابات موجودة
  const existingAccounts = {
    'Asset': ['1000'],
    'Liability': ['2000'], 
    'Equity': ['3000'],
    'Income': ['4000'],
    'Expense': ['5000'],
  };

  Object.entries(existingAccounts).forEach(([type, accounts]) => {
    const baseNumber = rootTypeNumbers[type];
    let maxNumber = parseInt(baseNumber);
    
    for (const accountNumber of accounts) {
      const num = parseInt(accountNumber);
      if (!isNaN(num) && num >= maxNumber) {
        maxNumber = num;
      }
    }

    // إذا لم توجد حسابات، إرجاع الرقم الأساسي
    if (maxNumber === parseInt(baseNumber)) {
      console.log(`✅ ${type}: الحساب الأول -> ${baseNumber}`);
    } else {
      // إضافة 1000 للرقم التالي
      const nextNumber = maxNumber + 1000;
      console.log(`✅ ${type}: الحساب التالي -> ${nextNumber}`);
    }
  });
}

// محاكاة منطق ترقيم الحسابات الفرعية
function testChildAccountNumbering() {
  console.log('\n\n🧪 اختبار ترقيم الحسابات الفرعية...\n');

  const testCases = [
    {
      parentNumber: '1000',
      existingSiblings: [],
      expectedResult: '1100',
      description: 'أول حساب فرعي تحت 1000'
    },
    {
      parentNumber: '1000', 
      existingSiblings: ['1100'],
      expectedResult: '1200',
      description: 'ثاني حساب فرعي تحت 1000'
    },
    {
      parentNumber: '1100',
      existingSiblings: [],
      expectedResult: '1110', 
      description: 'أول حساب فرعي تحت 1100'
    },
    {
      parentNumber: '1100',
      existingSiblings: ['1110'],
      expectedResult: '1120',
      description: 'ثاني حساب فرعي تحت 1100'
    },
    {
      parentNumber: '1110',
      existingSiblings: [],
      expectedResult: '1111',
      description: 'أول حساب فرعي تحت 1110'
    },
    {
      parentNumber: '1110',
      existingSiblings: ['1111', '1112'],
      expectedResult: '1113',
      description: 'ثالث حساب فرعي تحت 1110'
    }
  ];

  testCases.forEach(testCase => {
    const result = calculateChildAccountNumber(
      testCase.parentNumber, 
      testCase.existingSiblings
    );
    
    const status = result === testCase.expectedResult ? '✅' : '❌';
    console.log(`${status} ${testCase.description}`);
    console.log(`   الوالد: ${testCase.parentNumber}`);
    console.log(`   الموجود: [${testCase.existingSiblings.join(', ')}]`);
    console.log(`   المتوقع: ${testCase.expectedResult}`);
    console.log(`   النتيجة: ${result}`);
    console.log('');
  });
}

function calculateChildAccountNumber(parentNumber, existingSiblings) {
  const parentNumberInt = parseInt(parentNumber);
  let increment;

  // تحديد الزيادة بناءً على طول رقم الوالد
  if (parentNumber.length === 4) {
    // المستوى الأول: 1000 -> 1100, 1200, 1300...
    increment = 100;
  } else if (parentNumber.length === 5) {
    // المستوى الثاني: 1100 -> 1110, 1120, 1130...
    increment = 10;
  } else {
    // المستوى الثالث وما بعده: 1110 -> 1111, 1112, 1113...
    increment = 1;
  }

  // إذا لم توجد حسابات شقيقة، ابدأ من رقم الوالد + الزيادة
  if (existingSiblings.length === 0) {
    return (parentNumberInt + increment).toString();
  }

  // البحث عن أعلى رقم مستخدم من الحسابات الشقيقة
  let maxNumber = parentNumberInt; // ابدأ من رقم الوالد
  for (const accountNumber of existingSiblings) {
    const num = parseInt(accountNumber);
    if (!isNaN(num) && num > maxNumber) {
      maxNumber = num;
    }
  }

  // إنشاء الرقم التالي
  const nextNumber = maxNumber + increment;
  return nextNumber.toString();
}

// تشغيل الاختبارات
function runTests() {
  console.log('🔢 اختبار نظام ترقيم الحسابات التلقائي');
  console.log('='.repeat(50));
  
  testRootAccountNumbering();
  testChildAccountNumbering();
  
  console.log('🎉 انتهى الاختبار!');
}

// تشغيل الاختبارات
runTests();
