# خطوات اختبار نظام الترخيص - دليل سريع

## 🎯 الهدف
التأكد من أن أداة إنشاء التراخيص والنظام الأساسي يعملان معاً بشكل صحيح.

---

## 📋 الخطوات السريعة

### الخطوة 1: تشغيل أداة إنشاء التراخيص
```
📁 المكان: license-generator-app/dist/مولد التراخيص Setup 1.0.0.exe
🔧 الإجراء: تشغيل الملف كمدير
```

### الخطوة 2: الحصول على رقم تسلسلي للاختبار
```
🖱️ اضغط على: "رقمي التسلسلي"
📋 انسخ الرقم: مثل ERR-MC0L-GIWQ
```

### الخطوة 3: اختبار الخوارزمية في الأداة
```
🧪 اضغط على: "اختبار الخوارزمية"
👀 راقب النتائج: يجب أن تظهر مفاتيح صحيحة
📋 انسخ المفتاح الدائم: للاختبار في النظام
```

### الخطوة 4: تشغيل النظام الأساسي
```
📁 المكان: dist_electron/bundled/newsmart Setup 0.29.0.exe
🔧 الإجراء: تشغيل الملف
```

### الخطوة 5: اختبار التفعيل
```
📝 أدخل المفتاح: في صفحة تفعيل الترخيص
📝 أدخل معلومات الشركة: اسم وبريد إلكتروني
✅ اضغط تفعيل: يجب أن ينجح التفعيل
```

---

## 🔍 النتائج المتوقعة

### ✅ في أداة إنشاء التراخيص:
- عرض الرقم التسلسلي بشكل صحيح
- إنشاء مفاتيح بالتنسيق الصحيح
- عرض رسالة نجاح الاختبار

### ✅ في النظام الأساسي:
- قبول المفتاح المُنشأ
- تفعيل الترخيص بنجاح
- عرض معلومات الترخيص

---

## ❌ المشاكل المحتملة وحلولها

### المشكلة: "فشل في تفعيل الترخيص"
```
🔍 السبب المحتمل: عدم تطابق الرقم التسلسلي
✅ الحل: تأكد من استخدام نفس الرقم التسلسلي في كلا النظامين
```

### المشكلة: "تنسيق مفتاح الترخيص غير صحيح"
```
🔍 السبب المحتمل: خطأ في نسخ المفتاح
✅ الحل: انسخ المفتاح بالكامل بدون مسافات إضافية
```

### المشكلة: "مفتاح الترخيص غير صحيح لهذا الجهاز"
```
🔍 السبب المحتمل: المفتاح تم إنشاؤه لجهاز آخر
✅ الحل: أنشئ مفتاح جديد باستخدام الرقم التسلسلي الصحيح
```

---

## 🧪 اختبار متقدم (للمطورين)

### اختبار صفحة LicenseTest:
```
📁 المكان: src/pages/LicenseTest.vue
🔧 الوصول: للمطورين فقط
🧪 الوظائف:
  - عرض الرقم التسلسلي الحالي
  - اختبار إنشاء المفاتيح
  - اختبار مفاتيح مخصصة
  - عرض معلومات تقنية
```

### خطوات الاختبار المتقدم:
1. **الوصول للصفحة**: إضافة `/license-test` للرابط
2. **اختبار إنشاء المفاتيح**: الضغط على "اختبار إنشاء المفاتيح"
3. **اختبار مفتاح مخصص**: إدخال مفتاح من الأداة
4. **مراجعة النتائج**: التأكد من صحة جميع الاختبارات

---

## 📊 مثال عملي كامل

### السيناريو:
```
العميل: شركة الأعمال المتقدمة
الحاجة: ترخيص دائم للنظام
```

### الخطوات:
```
1️⃣ العميل يشغل النظام ويحصل على: ERR-MC0L-GIWQ
2️⃣ المطور يفتح أداة إنشاء التراخيص
3️⃣ المطور يدخل الرقم: ERR-MC0L-GIWQ
4️⃣ المطور يختار: دائم (Permanent)
5️⃣ المطور يضغط: إنشاء مفتاح الترخيص
6️⃣ الأداة تنشئ: PRM-6825-81C8-A2C8-5FAA
7️⃣ المطور يرسل المفتاح للعميل
8️⃣ العميل يدخل المفتاح في النظام
9️⃣ النظام يقبل المفتاح ويفعل الترخيص
🔟 العميل يستخدم النظام بنجاح
```

---

## 🎯 نقاط التحقق السريعة

### ✅ قائمة التحقق:
- [ ] أداة إنشاء التراخيص تعمل
- [ ] يمكن الحصول على رقم تسلسلي
- [ ] يمكن إنشاء مفاتيح صحيحة
- [ ] النظام الأساسي يقبل المفاتيح
- [ ] التفعيل يتم بنجاح
- [ ] معلومات الترخيص تظهر بشكل صحيح

### 🚨 علامات الخطر:
- ❌ رسائل خطأ في الكونسول
- ❌ فشل في إنشاء المفاتيح
- ❌ رفض المفاتيح في النظام
- ❌ عدم تفعيل الترخيص

---

## 📞 الدعم الفني

### في حالة مواجهة مشاكل:
```
📧 البريد الإلكتروني: <EMAIL>
📱 الهاتف: +966-XX-XXX-XXXX
💬 الدردشة: متوفرة في الأداة
```

### معلومات مطلوبة عند التواصل:
```
🔢 إصدار الأداة: 1.0.0
🔢 إصدار النظام: 0.29.0
📝 وصف المشكلة: تفصيلي
🖼️ لقطة شاشة: إن أمكن
📋 رسالة الخطأ: نص كامل
```

---

## 🎉 النتيجة النهائية

عند اتباع هذه الخطوات بشكل صحيح، يجب أن تحصل على:

### ✅ نظام ترخيص يعمل بكفاءة:
- مفاتيح صحيحة ومقبولة
- تفعيل ناجح للتراخيص
- أمان عالي ومرتبط بالجهاز
- سهولة في الاستخدام

### 🔒 ضمانات الأمان:
- كل مفتاح مرتبط بجهاز واحد
- لا يمكن تزوير المفاتيح
- التحقق محلي وآمن

**🎯 الهدف محقق: نظام ترخيص موثوق وقابل للاستخدام!**

---

**📅 تاريخ الدليل**: 17 ديسمبر 2024
**🔢 الإصدار**: 1.0.0
**👨‍💻 المطور**: Moneer al shawea
