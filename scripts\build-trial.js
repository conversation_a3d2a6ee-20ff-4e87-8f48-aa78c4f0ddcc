// سكريبت بناء النسخة التجريبية المدمجة الترخيص
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 بدء بناء النسخة التجريبية المدمجة الترخيص...');

// مسار ملف الترخيص المدمج
const embeddedLicensePath = path.join(__dirname, '../src/utils/embeddedLicense.ts');

// قراءة الملف الحالي
let fileContent = fs.readFileSync(embeddedLicensePath, 'utf8');

// النسخة الاحتياطية من الملف الأصلي
const backupPath = embeddedLicensePath + '.backup';
fs.writeFileSync(backupPath, fileContent);

console.log('📝 تحديث إعدادات الترخيص للنسخة التجريبية...');

// تحديث الإعدادات للنسخة التجريبية
fileContent = fileContent.replace(
  'isEmbedded: false,',
  'isEmbedded: true,'
);

fileContent = fileContent.replace(
  'licenseType: \'permanent\',',
  'licenseType: \'trial\','
);

fileContent = fileContent.replace(
  'maxUsers: 50,',
  'maxUsers: 3,'
);

fileContent = fileContent.replace(
  'allowedModules: [\'all\'],',
  'allowedModules: [\'sales\', \'inventory\', \'common\'],'
);

fileContent = fileContent.replace(
  'clientName: \'عميل مخصص\',',
  'clientName: \'نسخة تجريبية 30 يوم\','
);

fileContent = fileContent.replace(
  'trialDays: 30',
  'trialDays: 30'
);

// كتابة الملف المحدث
fs.writeFileSync(embeddedLicensePath, fileContent);

console.log('✅ تم تحديث إعدادات الترخيص');

try {
  console.log('🔨 بناء النظام...');
  
  // بناء النظام
  execSync('npm run build', { 
    stdio: 'inherit',
    cwd: path.join(__dirname, '..')
  });
  
  console.log('✅ تم بناء النظام بنجاح');
  
  // نسخ الملفات إلى مجلد التوزيع
  const sourceSetup = path.join(__dirname, '../dist_electron/bundled/newsmart Setup 0.29.0.exe');
  const sourcePortable = path.join(__dirname, '../dist_electron/bundled/newsmart 0.29.0.exe');
  const distDir = path.join(__dirname, '../distribution');
  
  // إنشاء مجلد التوزيع إذا لم يكن موجود
  if (!fs.existsSync(distDir)) {
    fs.mkdirSync(distDir, { recursive: true });
  }
  
  // نسخ النسخة التجريبية
  if (fs.existsSync(sourceSetup)) {
    const targetSetup = path.join(distDir, 'newsmart-trial-30days.exe');
    fs.copyFileSync(sourceSetup, targetSetup);
    console.log('✅ تم نسخ نسخة التثبيت التجريبية:', targetSetup);
  }
  
  if (fs.existsSync(sourcePortable)) {
    const targetPortable = path.join(distDir, 'newsmart-trial-portable.exe');
    fs.copyFileSync(sourcePortable, targetPortable);
    console.log('✅ تم نسخ النسخة المحمولة التجريبية:', targetPortable);
  }
  
} catch (error) {
  console.error('❌ خطأ في البناء:', error.message);
} finally {
  // استعادة الملف الأصلي
  console.log('🔄 استعادة الملف الأصلي...');
  fs.writeFileSync(embeddedLicensePath, fs.readFileSync(backupPath, 'utf8'));
  fs.unlinkSync(backupPath);
  console.log('✅ تم استعادة الملف الأصلي');
}

console.log('🎉 انتهى بناء النسخة التجريبية!');
