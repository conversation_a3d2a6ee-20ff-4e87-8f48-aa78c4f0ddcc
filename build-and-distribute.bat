@echo off
title بناء وتوزيع نظام newsmart المحاسبي
color 0A
chcp 65001 >nul

echo ========================================
echo    بناء وتوزيع نظام newsmart المحاسبي
echo ========================================
echo.

REM تحديد متغيرات البيئة
set BUILD_DATE=%date%
set BUILD_TIME=%time%
set VERSION=1.0.0

echo معلومات البناء:
echo التاريخ: %BUILD_DATE%
echo الوقت: %BUILD_TIME%
echo الإصدار: %VERSION%
echo.

echo [1/10] فحص متطلبات النظام...
node --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Node.js غير مثبت!
    echo يرجى تثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

npm --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: npm غير متوفر!
    pause
    exit /b 1
)

echo ✅ Node.js و npm متوفران

echo [2/10] تنظيف المشروع...
if exist "dist" rmdir /s /q "dist"
if exist "dist_electron" rmdir /s /q "dist_electron"
if exist "distribution" rmdir /s /q "distribution"
if exist "node_modules\.cache" rmdir /s /q "node_modules\.cache"
echo ✅ تم تنظيف المشروع

echo [3/10] تثبيت التبعيات...
call npm install
if errorlevel 1 (
    echo ❌ فشل في تثبيت التبعيات
    goto error
)
echo ✅ تم تثبيت التبعيات

echo [4/10] فحص جودة الكود...
call npm run lint --if-present
if errorlevel 1 (
    echo ⚠️ تحذير: مشاكل في جودة الكود
    echo هل تريد المتابعة؟ (Y/N)
    set /p choice=
    if /i not "%choice%"=="Y" goto error
)
echo ✅ فحص الكود مكتمل

echo [5/10] تشغيل الاختبارات...
call npm test --if-present
if errorlevel 1 (
    echo ⚠️ تحذير: بعض الاختبارات فشلت
    echo هل تريد المتابعة؟ (Y/N)
    set /p choice=
    if /i not "%choice%"=="Y" goto error
)
echo ✅ الاختبارات مكتملة

echo [6/10] بناء التطبيق للإنتاج...
call npm run build
if errorlevel 1 (
    echo ❌ فشل في بناء التطبيق
    goto error
)
echo ✅ تم بناء التطبيق

echo [7/10] إنشاء ملف exe للويندوز...
call npx electron-builder --win --x64
if errorlevel 1 (
    echo ❌ فشل في إنشاء ملف exe
    goto error
)
echo ✅ تم إنشاء ملف exe

echo [8/10] إنشاء النسخة المحمولة...
call npx electron-builder --win --x64 --config.win.target=portable
if errorlevel 1 (
    echo ⚠️ تحذير: فشل في إنشاء النسخة المحمولة
)
echo ✅ النسخة المحمولة جاهزة

echo [9/10] إنشاء حزمة التوزيع...
call :create_distribution
if errorlevel 1 (
    echo ❌ فشل في إنشاء حزمة التوزيع
    goto error
)
echo ✅ حزمة التوزيع جاهزة

echo [10/10] اختبار الملفات النهائية...
call :test_files
if errorlevel 1 (
    echo ⚠️ تحذير: مشاكل في الاختبار النهائي
)
echo ✅ الاختبار النهائي مكتمل

echo.
echo ========================================
echo تم البناء والتوزيع بنجاح! ✅
echo ========================================
echo.
echo الملفات الجاهزة في مجلد: distribution\
dir distribution\ /b
echo.
echo معلومات الإصدار:
echo - التاريخ: %BUILD_DATE%
echo - الوقت: %BUILD_TIME%
echo - الإصدار: %VERSION%
echo.
echo هل تريد فتح مجلد التوزيع؟ (Y/N)
set /p choice=
if /i "%choice%"=="Y" explorer distribution\

pause
goto end

:create_distribution
echo إنشاء مجلد التوزيع...
if not exist "distribution" mkdir distribution

echo نسخ ملفات التثبيت...
for %%f in ("dist\*.exe") do (
    if "%%~nf"=="newsmart Setup" (
        copy "%%f" "distribution\newsmart-setup.exe" >nul
    ) else (
        copy "%%f" "distribution\newsmart-portable.exe" >nul
    )
)

echo إنشاء ملف README للعميل...
(
echo # دليل تثبيت وتشغيل نظام newsmart المحاسبي
echo.
echo ## متطلبات النظام:
echo - نظام التشغيل: Windows 10/11 ^(64-bit^)
echo - الذاكرة: 4 GB RAM كحد أدنى
echo - مساحة القرص: 500 MB مساحة فارغة
echo - دقة الشاشة: 1024x768 كحد أدنى
echo.
echo ## طرق التثبيت:
echo.
echo ### الطريقة الأولى: التثبيت الكامل ^(مستحسن^)
echo 1. شغل ملف "newsmart-setup.exe"
echo 2. اتبع تعليمات المثبت
echo 3. اختر مجلد التثبيت
echo 4. انتظر انتهاء التثبيت
echo 5. شغل البرنامج من قائمة ابدأ أو سطح المكتب
echo.
echo ### الطريقة الثانية: النسخة المحمولة
echo 1. شغل ملف "newsmart-portable.exe" مباشرة
echo 2. لا يحتاج تثبيت
echo 3. يمكن تشغيله من أي مكان ^(USB، مجلد، إلخ^)
echo.
echo ## أول تشغيل:
echo 1. عند أول تشغيل، سيطلب منك إنشاء شركة جديدة
echo 2. أدخل بيانات الشركة المطلوبة
echo 3. اختر العملة الافتراضية
echo 4. أنشئ حساب المدير الأول
echo.
echo ## الدعم الفني:
echo - البريد الإلكتروني: <EMAIL>
echo - الهاتف: +966-XX-XXX-XXXX
echo - الموقع: www.newsmart.com
echo.
echo ## ملاحظات مهمة:
echo - احتفظ بنسخة احتياطية من البيانات بانتظام
echo - لا تحذف مجلد البيانات في AppData
echo - تأكد من تشغيل البرنامج بصلاحيات المدير عند الحاجة
echo.
echo تاريخ الإصدار: %BUILD_DATE%
echo إصدار البرنامج: %VERSION%
) > "distribution\README-العميل.txt"

echo إنشاء سكريبت التثبيت السريع...
(
echo @echo off
echo title تثبيت نظام newsmart المحاسبي
echo echo ===================================
echo echo    تثبيت نظام newsmart المحاسبي
echo echo ===================================
echo echo.
echo echo جاري التثبيت...
echo newsmart-setup.exe /S
echo echo.
echo echo تم التثبيت بنجاح!
echo echo يمكنك الآن تشغيل البرنامج من قائمة ابدأ
echo echo.
echo pause
) > "distribution\تثبيت-سريع.bat"

echo إنشاء معلومات الإصدار...
(
echo معلومات إصدار نظام newsmart المحاسبي
echo ==========================================
echo.
echo تاريخ البناء: %BUILD_DATE%
echo وقت البناء: %BUILD_TIME%
echo رقم الإصدار: %VERSION%
echo.
echo الملفات المتضمنة:
) > "distribution\معلومات-الإصدار.txt"

dir "distribution\*.exe" >> "distribution\معلومات-الإصدار.txt"

exit /b 0

:test_files
echo فحص وجود الملفات المطلوبة...
if not exist "distribution\newsmart-setup.exe" (
    echo ❌ ملف المثبت غير موجود!
    exit /b 1
)

if not exist "distribution\README-العميل.txt" (
    echo ❌ ملف README غير موجود!
    exit /b 1
)

echo فحص أحجام الملفات...
for %%f in ("distribution\newsmart-setup.exe") do (
    if %%~zf LSS 10000000 (
        echo ⚠️ تحذير: حجم ملف المثبت صغير جداً ^(أقل من 10MB^)
    )
)

echo ✅ جميع الملفات موجودة وصحيحة
exit /b 0

:error
echo.
echo ========================================
echo حدث خطأ أثناء البناء! ❌
echo ========================================
echo.
echo يرجى مراجعة الأخطاء أعلاه وإعادة المحاولة
echo.
pause
exit /b 1

:end
echo.
echo شكراً لاستخدام أداة البناء التلقائي!
echo.
