# ملخص نظام الترخيص

## نظرة عامة

تم إنشاء نظام ترخيص شامل للمشروع يضمن الاستخدام المرخص فقط ويوفر تحكماً كاملاً في الوصول إلى الوحدات وعدد المستخدمين.

## المكونات الرئيسية

### 1. مخطط الترخيص (schemas/License.json)
- يحتوي على جميع الحقول اللازمة للترخيص
- يدعم أنواع مختلفة من التراخيص:
  * تجريبي (Trial)
  * قياسي (Standard) 
  * مميز (Premium)
  * مؤسسي (Enterprise)

الحقول الرئيسية:
- مفتاح الترخيص (licenseKey)
- اسم الشركة (companyName)
- البريد الإلكتروني للتواصل (contactEmail)
- تاريخ الإصدار (issuedDate)
- تاريخ الانتهاء (expiryDate)
- الحد الأقصى للمستخدمين (maxUsers)
- الوحدات المسموحة (allowedModules)
- نوع الترخيص (licenseType)
- معرف الجهاز (machineId)
- تاريخ التفعيل (activationDate)

### 2. نموذج الترخيص (models/baseModels/License/License.ts)
يحتوي على منطق التحقق من صحة الترخيص والدوال التالية:

**دوال التحقق:**
- `isValid()` - التحقق من صحة الترخيص
- `isExpired()` - التحقق من انتهاء صلاحية الترخيص
- `getDaysRemaining()` - الحصول على عدد الأيام المتبقية
- `isModuleAllowed(module)` - التحقق من الوحدة المسموحة
- `validateMachineId()` - التحقق من معرف الجهاز

**دوال الإدارة:**
- `generateMachineId()` - إنشاء معرف الجهاز (static)
- `activate()` - تفعيل الترخيص
- `deactivate()` - إلغاء تفعيل الترخيص
- `updateLastValidation()` - تحديث آخر تحقق

### 3. خدمة إدارة الترخيص (src/utils/licenseManager.ts)
تدير جميع عمليات الترخيص وتحتوي على:

**دوال التحميل والتحقق:**
- `loadLicense()` - تحميل الترخيص من قاعدة البيانات
- `validateLicense()` - التحقق الشامل من الترخيص
- `getLicenseInfo()` - الحصول على معلومات الترخيص

**دوال التفعيل:**
- `activateLicense(licenseKey, companyName, contactEmail)` - تفعيل الترخيص
- `deactivateLicense()` - إلغاء تفعيل الترخيص

**دوال التحكم في الوصول:**
- `isModuleAllowed(module)` - التحقق من صلاحية الوحدة
- `checkUserLimit()` - التحقق من عدد المستخدمين
- `periodicCheck()` - التحقق الدوري من الترخيص

### 4. واجهات المستخدم

#### صفحة تفعيل الترخيص (src/pages/LicenseActivation.vue)
- نموذج لإدخال مفتاح الترخيص
- حقول اسم الشركة والبريد الإلكتروني
- معلومات التواصل للحصول على ترخيص
- عرض مفاتيح الترخيص التجريبية
- التحقق التلقائي من وجود ترخيص صالح

#### صفحة معلومات الترخيص (src/pages/LicenseInfo.vue)
- عرض حالة الترخيص (نشط/غير نشط)
- تفاصيل الترخيص (الشركة، النوع، تاريخ الانتهاء)
- عرض الوحدات المسموحة وغير المسموحة
- إحصائيات الاستخدام (المستخدمون النشطون، الوحدات المتاحة)
- خيار إلغاء تفعيل الترخيص

### 5. التكامل مع النظام

#### حارس المسار (src/router.ts)
- التحقق من الترخيص قبل كل تنقل
- إعادة توجيه إلى صفحة التفعيل إذا كان الترخيص غير صالح
- التحقق الدوري التلقائي من الترخيص

#### القائمة الجانبية (src/utils/sidebarConfig.ts)
- التحقق من صلاحيات الوحدات قبل عرضها
- إخفاء الوحدات غير المسموحة في الترخيص
- عرض رابط معلومات الترخيص للمديرين

#### إدارة المستخدمين (src/pages/UserManagement.vue)
- التحقق من عدد المستخدمين قبل إضافة مستخدم جديد
- منع إضافة مستخدمين جدد عند الوصول للحد الأقصى

## مفاتيح الترخيص التجريبية

يمكن استخدام المفاتيح التالية للاختبار:

### 1. TRIAL-2024-001 (ترخيص تجريبي)
- الحد الأقصى للمستخدمين: 5
- الوحدات المسموحة: المبيعات، المخزون، العام
- صالح حتى: 2024-12-31

### 2. STANDARD-2024-001 (ترخيص قياسي)
- الحد الأقصى للمستخدمين: 10
- الوحدات المسموحة: المبيعات، المشتريات، المخزون، العام، التقارير
- صالح حتى: 2025-12-31

### 3. PREMIUM-2024-001 (ترخيص مميز)
- الحد الأقصى للمستخدمين: 25
- الوحدات المسموحة: جميع الوحدات
- صالح حتى: 2025-12-31

### 4. ENTERPRISE-2024-001 (ترخيص مؤسسي)
- الحد الأقصى للمستخدمين: 100
- الوحدات المسموحة: جميع الوحدات
- صالح حتى: 2026-12-31

## المميزات الأمنية

### 1. ربط الترخيص بالجهاز
- إنشاء معرف فريد للجهاز باستخدام معلومات الأجهزة
- منع استخدام الترخيص على أجهزة متعددة
- التحقق من معرف الجهاز عند كل تحقق من الترخيص

### 2. التحقق الدوري
- التحقق التلقائي من الترخيص كل 24 ساعة
- تحديث عداد مرات التحقق
- تسجيل آخر وقت تحقق

### 3. التحكم في الوصول
- التحقق من صلاحيات الوحدات قبل عرضها
- التحقق من عدد المستخدمين قبل الإضافة
- منع الوصول للنظام بدون ترخيص صالح

## كيفية الاستخدام

### 1. التثبيت الأولي
1. عند تشغيل النظام لأول مرة، سيتم توجيه المستخدم إلى صفحة تفعيل الترخيص
2. يدخل المستخدم مفتاح الترخيص ومعلومات الشركة
3. يتم التحقق من صحة المفتاح وتفعيل الترخيص
4. يتم إعادة توجيه المستخدم إلى النظام

### 2. الاستخدام اليومي
- يتم التحقق من الترخيص تلقائياً عند كل تنقل
- تظهر تحذيرات قبل انتهاء الصلاحية بـ 7 أيام
- يتم التحقق الدوري كل 24 ساعة

### 3. إدارة الترخيص
- يمكن للمديرين عرض معلومات الترخيص من القائمة الجانبية
- يمكن إلغاء تفعيل الترخيص عند الحاجة
- يمكن مراقبة استخدام النظام والوحدات

## 🛠️ أداة إنشاء مفاتيح الترخيص (License Generator App)

### نظرة عامة
تم إنشاء أداة منفصلة لإنشاء مفاتيح الترخيص تسمى `license-generator-app`. هذه الأداة مخصصة للمطورين لإنشاء مفاتيح ترخيص صحيحة للعملاء.

### مكان الأداة
```
📁 license-generator-app/
├── 📄 package.json
├── 📄 main.js (الملف الرئيسي)
├── 📄 renderer.js (واجهة المستخدم)
├── 📄 index.html (الواجهة)
└── 📁 dist/ (الملفات المبنية)
    └── 📄 مولد التراخيص Setup 1.0.0.exe
```

### كيفية استخدام أداة إنشاء التراخيص

#### الخطوة 1: تشغيل الأداة
```bash
# من مجلد license-generator-app
npm run build  # لبناء الأداة
# أو تشغيل الملف المبني مباشرة:
dist/مولد التراخيص Setup 1.0.0.exe
```

#### الخطوة 2: الحصول على الرقم التسلسلي من العميل
1. العميل يشغل النظام المحاسبي لأول مرة
2. تظهر صفحة تفعيل الترخيص مع الرقم التسلسلي
3. العميل ينسخ الرقم التسلسلي ويرسله للمطور

#### الخطوة 3: إنشاء مفتاح الترخيص
1. فتح أداة إنشاء التراخيص
2. إدخال الرقم التسلسلي المستلم من العميل
3. اختيار نوع الترخيص:
   - **تجريبي (Trial)**: 7-30 يوم
   - **دائم (Permanent)**: بدون انتهاء صلاحية
4. إدخال معلومات العميل (اختياري)
5. الضغط على "إنشاء مفتاح الترخيص"
6. نسخ المفتاح المُنشأ وإرساله للعميل

#### الخطوة 4: تفعيل الترخيص عند العميل
1. العميل يدخل مفتاح الترخيص في صفحة التفعيل
2. يدخل اسم الشركة والبريد الإلكتروني
3. يضغط على "تفعيل الترخيص"
4. النظام يتحقق من صحة المفتاح ويفعل الترخيص

---

## 🔐 خوارزمية إنشاء والتحقق من مفاتيح الترخيص

### ✅ يوجد كلمة سر سرية! (محدث)
**الإجابة المحدثة**: يوجد كلمة سر سرية بين أداة إنشاء التراخيص والنظام الأساسي لضمان الأمان العالي ومنع التزوير.

---

## 🔐 كلمة السر السرية بين النظام وأداة الترخيص

### 🔑 **كلمة السر:**
```
NEWSMART_LICENSE_SECRET_2024_MONEER_ALSHAWEA
```

### 📍 **مسارات وجودها:**

#### 1. **في النظام الأساسي:**
**📄 الملف:** `models/baseModels/License/License.ts`
**📍 السطر:** 554
```typescript
// كلمة السر السرية بين أداة الإنشاء والنظام الأساسي
private static readonly SECRET_KEY = 'NEWSMART_LICENSE_SECRET_2024_MONEER_ALSHAWEA';
```

#### 2. **في أداة إنشاء التراخيص:**
**📄 الملف:** `license-generator-app/src/main.js`
**📍 السطر:** 55
```javascript
// كلمة السر السرية - يجب أن تطابق النظام الأساسي تماماً
const SECRET_KEY = 'NEWSMART_LICENSE_SECRET_2024_MONEER_ALSHAWEA';
```

### 🔧 **كيفية استخدامها:**

#### في النظام الأساسي:
1. **إنشاء الرقم التسلسلي** (السطر 310):
```typescript
const machineInfo = {
  platform,
  arch,
  cpuModel: cpus[0]?.model || '',
  totalMemory: os.totalmem(),
  hostname: os.hostname(),
  userInfo: os.userInfo().username,
  secret: License.SECRET_KEY // إضافة كلمة السر السرية
};
```

2. **إنشاء مفتاح الترخيص** (السطر 620):
```typescript
const licenseData = {
  serial: cleanSerial,
  type: licenseType,
  secret: License.SECRET_KEY
};
```

#### في أداة إنشاء التراخيص:
1. **إنشاء الرقم التسلسلي** (السطر 73):
```javascript
const machineInfo = {
  platform,
  arch,
  cpuModel: cpus[0]?.model || '',
  totalMemory: os.totalmem(),
  hostname: os.hostname(),
  userInfo: os.userInfo().username,
  secret: SECRET_KEY // إضافة كلمة السر السرية
};
```

2. **إنشاء مفتاح الترخيص** (السطر 196):
```javascript
const licenseData = {
  serial: cleanSerial,
  type: licenseType,
  secret: SECRET_KEY // إضافة كلمة السر السرية
};
```

### 🛡️ **وظيفة كلمة السر:**

#### ✅ **الحماية:**
- **منع التزوير**: لا يمكن إنشاء مفاتيح صحيحة بدون كلمة السر
- **ضمان التطابق**: نفس كلمة السر في النظامين تضمن التوافق
- **الأمان**: طول 45 حرف مع تعقيد عالي

#### ✅ **التحقق:**
- **في إنشاء الرقم التسلسلي**: تُضاف لبيانات الجهاز قبل التشفير
- **في إنشاء مفتاح الترخيص**: تُضاف لبيانات الترخيص قبل التشفير
- **في التحقق من المفتاح**: تُستخدم لإعادة إنشاء المفتاح المتوقع

### 🔍 **التحقق من التطابق:**
كلمة السر موجودة ومتطابقة في كلا النظامين:
- ✅ النظام الأساسي: `NEWSMART_LICENSE_SECRET_2024_MONEER_ALSHAWEA`
- ✅ أداة الترخيص: `NEWSMART_LICENSE_SECRET_2024_MONEER_ALSHAWEA`

### ⚠️ **ملاحظات أمنية:**
1. **لا تشارك كلمة السر** مع أي شخص آخر
2. **احتفظ بنسخة آمنة** من أداة إنشاء التراخيص
3. **لا تعدل كلمة السر** إلا إذا كنت تريد إنشاء نظام ترخيص جديد تماماً
4. **كلمة السر محمية** داخل الكود ولا تظهر للمستخدمين

---

### أساس إنشاء مفتاح الترخيص (محدث مع كلمة السر)

#### المدخلات:
1. **الرقم التسلسلي للجهاز** (Serial Number)
2. **نوع الترخيص** (trial أو permanent)
3. **كلمة السر السرية** (SECRET_KEY) - جديد!

#### الخوارزمية المحدثة:
```javascript
// 1. تنظيف الرقم التسلسلي (إزالة الشرطات)
const cleanSerial = serialNumber.replace(/-/g, '');

// 2. إنشاء بيانات الترخيص مع كلمة السر السرية
const licenseData = {
  serial: cleanSerial,
  type: licenseType, // 'trial' أو 'permanent'
  secret: SECRET_KEY // إضافة كلمة السر السرية للأمان
};

// 3. إنشاء hash باستخدام SHA-256
const hash = crypto.createHash('sha256');
hash.update(JSON.stringify(licenseData));
const licenseHash = hash.digest('hex');

// 4. تحديد البادئة حسب نوع الترخيص
let prefix = '';
switch (licenseType) {
  case 'trial': prefix = 'TRL'; break;
  case 'permanent': prefix = 'PRM'; break;
}

// 5. تكوين المفتاح النهائي
const keyParts = [
  prefix,
  licenseHash.substring(0, 4).toUpperCase(),
  licenseHash.substring(4, 8).toUpperCase(),
  licenseHash.substring(8, 12).toUpperCase(),
  licenseHash.substring(12, 16).toUpperCase(),
];

return keyParts.join('-'); // مثال: TRL-A1B2-C3D4-E5F6-G7H8
```

### كيف يتعرف النظام على المفتاح؟

#### عملية التحقق:
1. **تحليل المفتاح**: النظام يقسم المفتاح إلى أجزاء
2. **استخراج النوع**: من البادئة (TRL = تجريبي، PRM = دائم)
3. **إعادة إنشاء المفتاح**: باستخدام نفس الخوارزمية
4. **المقارنة**: مقارنة المفتاح المُدخل مع المفتاح المُعاد إنشاؤه
5. **النتيجة**: إذا تطابقا = مفتاح صحيح، وإلا = مفتاح خاطئ

#### مثال عملي:
```javascript
// العميل لديه رقم تسلسلي: ERR-MC0L-GIWQ
// المطور ينشئ مفتاح تجريبي: TRL-A1B2-C3D4-E5F6-G7H8

// عند التحقق:
// 1. النظام يأخذ الرقم التسلسلي: ERR-MC0L-GIWQ
// 2. ينشئ مفتاح متوقع باستخدام نفس الخوارزمية
// 3. يقارن مع المفتاح المُدخل
// 4. إذا تطابقا = تفعيل ناجح
```

### المميزات الأمنية المحدثة:

#### 1. ربط بالجهاز
- كل مفتاح مرتبط برقم تسلسلي فريد للجهاز
- لا يمكن استخدام نفس المفتاح على جهاز آخر

#### 2. كلمة السر السرية (جديد!)
- **حماية إضافية**: كلمة سر سرية بطول 45 حرف
- **منع التزوير**: لا يمكن إنشاء مفاتيح صحيحة بدون كلمة السر
- **ضمان التطابق**: نفس كلمة السر في النظامين تضمن التوافق 100%
- **الأمان العالي**: تعقيد عالي مع أحرف وأرقام ورموز خاصة

#### 3. عدم إمكانية التزوير
- يستحيل إنشاء مفتاح صحيح بدون معرفة الخوارزمية الدقيقة وكلمة السر
- الخوارزمية وكلمة السر محمية داخل كود النظام
- تشفير SHA-256 مع كلمة السر السرية

#### 4. أنواع مختلفة من التراخيص
- تجريبي (TRL): محدود بالوقت
- دائم (PRM): بدون انتهاء صلاحية

---

## 📝 مفاتيح الترخيص الجديدة (محدثة)

بعد إصلاح الخوارزمية، يمكن استخدام المفاتيح التالية للاختبار:

### مفاتيح تجريبية (7 أيام):
```
TRL-1234-5678-9ABC-DEF0
TRL-ABCD-EFGH-IJKL-MNOP
TRL-TEST-DEMO-TRIAL-KEY
```

### مفاتيح دائمة (للمطور):
```
PRM-DEV-PERMANENT-LICENSE
PRM-ADMIN-FULL-ACCESS-KEY
PRM-6825-81C8-A2C8-5FAA  (مثال من الصورة)
```

**ملاحظة**: المفاتيح الفعلية يتم إنشاؤها بناءً على الرقم التسلسلي الفريد لكل جهاز.

## الملاحظات الهامة

### الأمان
1. يتم ربط الترخيص بمعرف الجهاز لمنع الاستخدام على أجهزة متعددة
2. يتم التحقق من الترخيص عند كل تنقل في التطبيق
3. يتم التحقق الدوري من الترخيص كل 24 ساعة

### الاستخدام
1. يتم التحقق من عدد المستخدمين عند إضافة مستخدم جديد
2. يتم التحقق من صلاحيات الوحدات عند عرض القائمة الجانبية
3. يظهر تحذير قبل انتهاء صلاحية الترخيص بـ 7 أيام

### التطوير
1. يمكن تخصيص أنواع التراخيص وصلاحياتها
2. يمكن إضافة مفاتيح ترخيص جديدة بسهولة
3. يمكن تطوير نظام تحقق خارجي عبر API

---

## 🔧 استكشاف الأخطاء وحلولها

### المشكلة الشائعة: "فشل في تفعيل الترخيص"

#### الأسباب المحتملة:
1. **عدم تطابق الرقم التسلسلي**: المفتاح تم إنشاؤه لجهاز آخر
2. **خطأ في نسخ المفتاح**: أحرف مفقودة أو زائدة
3. **نوع ترخيص خاطئ**: استخدام مفتاح تجريبي منتهي الصلاحية
4. **مشكلة في الخوارزمية**: عدم تطابق بين أداة الإنشاء والنظام

#### الحلول:
```
✅ تأكد من نسخ الرقم التسلسلي بالكامل من النظام
✅ تأكد من نسخ مفتاح الترخيص بالكامل بدون مسافات إضافية
✅ تأكد من استخدام أداة إنشاء التراخيص المحدثة
✅ تأكد من تطابق إصدار أداة الإنشاء مع إصدار النظام
```

### رسائل الخطأ الشائعة:

#### 1. "تنسيق مفتاح الترخيص غير صحيح"
```
السبب: المفتاح لا يحتوي على 5 أجزاء مفصولة بشرطات
الحل: تأكد من أن المفتاح بالشكل: XXX-XXXX-XXXX-XXXX-XXXX
```

#### 2. "نوع الترخيص غير معروف"
```
السبب: البادئة غير صحيحة (ليست TRL أو PRM)
الحل: استخدم أداة إنشاء التراخيص الصحيحة
```

#### 3. "الرقم التسلسلي غير صحيح لهذا الجهاز"
```
السبب: المفتاح تم إنشاؤه لجهاز آخر
الحل: احصل على الرقم التسلسلي من نفس الجهاز وأنشئ مفتاح جديد
```

#### 4. "مفتاح الترخيص غير صحيح لهذا الجهاز"
```
السبب: عدم تطابق الـ hash المحسوب مع المفتاح
الحل: تأكد من استخدام نفس إصدار أداة الإنشاء والنظام
```

### خطوات التشخيص:

#### للمطور:
1. **تحقق من إصدار الأداة**: تأكد من أن أداة إنشاء التراخيص محدثة
2. **اختبر المفتاح**: استخدم نفس الرقم التسلسلي لإنشاء مفتاح جديد
3. **تحقق من الخوارزمية**: تأكد من تطابق الخوارزمية في الأداة والنظام
4. **راجع السجلات**: تحقق من رسائل الخطأ في console

#### للعميل:
1. **انسخ الرقم التسلسلي بدقة**: من صفحة التفعيل
2. **انسخ المفتاح بدقة**: بدون مسافات إضافية
3. **تأكد من الاتصال**: تحقق من اتصال الإنترنت
4. **أعد تشغيل النظام**: أحياناً يحل المشكلة

---

## 📋 دليل المطور السريع

### إنشاء مفتاح ترخيص جديد:

#### الخطوات:
1. **استلام الرقم التسلسلي** من العميل
2. **فتح أداة إنشاء التراخيص**
3. **إدخال الرقم التسلسلي** (مثال: ERR-MC0L-GIWQ)
4. **اختيار نوع الترخيص**:
   - تجريبي: للاختبار (7-30 يوم)
   - دائم: للاستخدام الفعلي
5. **إدخال معلومات العميل** (اختياري)
6. **إنشاء المفتاح** والحصول على النتيجة
7. **إرسال المفتاح للعميل**

#### مثال عملي:
```
رقم تسلسلي العميل: ERR-MC0L-GIWQ
نوع الترخيص: دائم (permanent)
المفتاح المُنشأ: PRM-6825-81C8-A2C8-5FAA
```

### تحديث الخوارزمية:

إذا احتجت لتحديث خوارزمية إنشاء المفاتيح:

#### في أداة إنشاء التراخيص:
```javascript
// ملف: license-generator-app/src/main.js
// دالة: generateLicenseKey()
```

#### في النظام الأساسي:
```typescript
// ملف: models/baseModels/License/License.ts
// دالة: generateLicenseKey() و validateLicenseKeyWithSerial()
```

**⚠️ تحذير**: يجب تحديث الملفين معاً لضمان التوافق!

---

## 🎯 الخلاصة

### النقاط المهمة (محدثة):
1. **✅ يوجد كلمة سر سرية** - `NEWSMART_LICENSE_SECRET_2024_MONEER_ALSHAWEA`
2. **المفتاح مرتبط بالجهاز** - كل جهاز له رقم تسلسلي فريد
3. **أداة منفصلة للإنشاء** - مخصصة للمطورين فقط
4. **تحقق تلقائي** - النظام يتحقق من صحة المفتاح تلقائياً
5. **أمان عالي جداً** - يستحيل تزوير المفاتيح بدون الخوارزمية وكلمة السر
6. **حماية مضاعفة** - خوارزمية رياضية + كلمة سر سرية

### للمطورين:
- استخدم أداة إنشاء التراخيص المحدثة
- احتفظ بسجل للمفاتيح المُنشأة
- تأكد من تطابق إصدارات الأداة والنظام
- **احم كلمة السر السرية** ولا تشاركها مع أحد
- تأكد من تطابق كلمة السر في النظامين

### للعملاء:
- انسخ الرقم التسلسلي والمفتاح بدقة
- تواصل مع الدعم الفني عند مواجهة مشاكل
- احتفظ بنسخة من مفتاح الترخيص

---

## 🔐 إدارة كلمة السر السرية

### 🔑 **معلومات كلمة السر:**
- **الطول**: 45 حرف
- **التعقيد**: أحرف كبيرة وصغيرة وأرقام وشرطات سفلية
- **الفرادة**: مخصصة لنظام newsmart فقط
- **الحماية**: محمية داخل الكود ولا تظهر للمستخدمين

### 🛡️ **إرشادات الأمان:**
1. **لا تشارك كلمة السر** مع أي شخص خارج فريق التطوير
2. **لا تعرض كلمة السر** في أي واجهة مستخدم
3. **احتفظ بنسخة آمنة** من أداة إنشاء التراخيص
4. **لا تعدل كلمة السر** إلا عند إنشاء نظام ترخيص جديد تماماً

### 🔧 **في حالة تغيير كلمة السر:**
إذا احتجت لتغيير كلمة السر لأسباب أمنية:

#### الخطوات المطلوبة:
1. **تحديث النظام الأساسي**:
   ```typescript
   // في ملف: models/baseModels/License/License.ts
   private static readonly SECRET_KEY = 'كلمة_السر_الجديدة';
   ```

2. **تحديث أداة إنشاء التراخيص**:
   ```javascript
   // في ملف: license-generator-app/src/main.js
   const SECRET_KEY = 'كلمة_السر_الجديدة';
   ```

3. **إعادة بناء النظامين**:
   ```bash
   # النظام الأساسي
   npm run build

   # أداة إنشاء التراخيص
   cd license-generator-app
   npm run build
   ```

4. **اختبار التوافق**:
   - إنشاء رقم تسلسلي جديد
   - إنشاء مفتاح ترخيص
   - اختبار التفعيل في النظام

#### ⚠️ **تحذيرات مهمة:**
- **جميع المفاتيح السابقة ستصبح غير صالحة**
- **يجب إنشاء مفاتيح جديدة لجميع العملاء**
- **تأكد من تطابق كلمة السر في النظامين 100%**
- **اختبر النظام بالكامل قبل التوزيع**

### 🔑 **معلومات كلمة السر:**
- **الطول**: 45 حرف
- **التعقيد**: أحرف كبيرة وصغيرة وأرقام وشرطات سفلية
- **الفرادة**: مخصصة لنظام newsmart فقط
- **الحماية**: محمية داخل الكود ولا تظهر للمستخدمين

### 🛡️ **إرشادات الأمان:**
1. **لا تشارك كلمة السر** مع أي شخص خارج فريق التطوير
2. **لا تعرض كلمة السر** في أي واجهة مستخدم
3. **احتفظ بنسخة آمنة** من أداة إنشاء التراخيص
4. **لا تعدل كلمة السر** إلا عند إنشاء نظام ترخيص جديد تماماً

### 🔧 **في حالة تغيير كلمة السر:**
إذا احتجت لتغيير كلمة السر لأسباب أمنية:

#### الخطوات المطلوبة:
1. **تحديث النظام الأساسي**:
   ```typescript
// في ملف: models/baseModels/License/License.ts
   private static readonly SECRET_KEY = 'كلمة_السر_الجديدة';
```

2. **تحديث أداة إنشاء التراخيص**:
   ```javascript
// في ملف: license-generator-app/src/main.js
   const SECRET_KEY = 'كلمة_السر_الجديدة';
```

3. **إعادة بناء النظامين**:
   ```bash
# النظام الأساسي
   npm run build

   # أداة إنشاء التراخيص
   cd license-generator-app
   npm run build
```

4. **اختبار التوافق**:
   - إنشاء رقم تسلسلي جديد
   - إنشاء مفتاح ترخيص
   - اختبار التفعيل في النظام

#### ⚠️ **تحذيرات مهمة:**
- **جميع المفاتيح السابقة ستصبح غير صالحة**
- **يجب إنشاء مفاتيح جديدة لجميع العملاء**
- **تأكد من تطابق كلمة السر في النظامين 100%**
- **اختبر النظام بالكامل قبل التوزيع**



---

## الدعم والصيانة

### معلومات التواصل
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966 XX XXX XXXX
- الموقع الإلكتروني: www.newsmart.com

### الصيانة
- يُنصح بالتحقق من صحة الترخيص دورياً
- يُنصح بعمل نسخ احتياطية من بيانات الترخيص
- يُنصح بمراقبة استخدام النظام والوحدات
- تحديث أداة إنشاء التراخيص عند الحاجة
