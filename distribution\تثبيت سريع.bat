@echo off
title تثبيت نظام newsmart المحاسبي
color 0A
chcp 65001 >nul

echo ========================================
echo    تثبيت نظام newsmart المحاسبي
echo ========================================
echo.

echo مرحباً بك في مثبت نظام newsmart المحاسبي
echo.

echo الملفات المتاحة:
echo [1] المثبت الكامل (مستحسن)
echo [2] النسخة المحمولة
echo [3] عرض معلومات الإصدار
echo [4] إلغاء
echo.

set /p choice="اختر رقم الخيار المطلوب: "

if "%choice%"=="1" goto install_full
if "%choice%"=="2" goto run_portable
if "%choice%"=="3" goto show_info
if "%choice%"=="4" goto cancel
goto invalid_choice

:install_full
echo.
echo ========================================
echo تثبيت النسخة الكاملة
echo ========================================
echo.

if not exist "newsmart-setup.exe" (
    echo ❌ خطأ: ملف المثبت غير موجود!
    echo تأكد من وجود ملف newsmart-setup.exe في نفس المجلد
    pause
    goto end
)

echo جاري تشغيل المثبت...
echo.
echo ملاحظات مهمة:
echo - سيتم تثبيت النظام في Program Files
echo - ستظهر اختصارات في سطح المكتب وقائمة ابدأ
echo - عند أول تشغيل ستحتاج لتفعيل الترخيص
echo.

start "" "newsmart-setup.exe"

echo تم تشغيل المثبت بنجاح!
echo اتبع التعليمات التي تظهر على الشاشة
echo.
pause
goto end

:run_portable
echo.
echo ========================================
echo تشغيل النسخة المحمولة
echo ========================================
echo.

if not exist "newsmart-portable.exe" (
    echo ❌ خطأ: ملف النسخة المحمولة غير موجود!
    echo تأكد من وجود ملف newsmart-portable.exe في نفس المجلد
    pause
    goto end
)

echo جاري تشغيل النسخة المحمولة...
echo.
echo ملاحظات مهمة:
echo - لا يحتاج تثبيت
echo - يعمل مباشرة من هذا المجلد
echo - عند أول تشغيل ستحتاج لتفعيل الترخيص
echo.

start "" "newsmart-portable.exe"

echo تم تشغيل النسخة المحمولة بنجاح!
echo.
pause
goto end

:show_info
echo.
echo ========================================
echo معلومات الإصدار
echo ========================================
echo.

if exist "معلومات الإصدار.txt" (
    type "معلومات الإصدار.txt"
) else (
    echo اسم النظام: newsmart المحاسبي
    echo رقم الإصدار: 0.29.0
    echo تاريخ البناء: 17 ديسمبر 2024
    echo المطور: Moneer al shawea
    echo.
    echo الملفات المتضمنة:
    dir *.exe /b
)

echo.
pause
goto end

:invalid_choice
echo.
echo ❌ خيار غير صحيح! يرجى اختيار رقم من 1 إلى 4
echo.
pause
goto end

:cancel
echo.
echo تم إلغاء العملية
echo.
pause
goto end

:end
echo.
echo شكراً لاستخدام نظام newsmart المحاسبي!
echo للدعم الفني: <EMAIL>
echo.
