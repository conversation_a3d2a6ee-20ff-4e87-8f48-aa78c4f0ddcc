import { Doc } from 'fyo/model/doc';
import { ValidationMap } from 'fyo/model/types';
import { validateEmail } from 'src/utils/validation';

// تصحيح الخطأ: استخدام require بدلاً من import للوحدات النظام
// import { createHash } from 'crypto';

export class License extends Doc {
  licenseKey?: string;
  companyName?: string;
  contactEmail?: string;
  contactPhone?: string;
  issuedDate?: Date;
  expiryDate?: Date;
  maxUsers?: number;
  allowedModules?: string;
  isActive?: number;
  licenseType?: 'trial' | 'standard' | 'premium' | 'enterprise';
  machineId?: string;
  activationDate?: Date;
  lastValidation?: Date;
  validationCount?: number;

  // تصحيح الخطأ: إزالة التحقق من البريد الإلكتروني مؤقتاً
  // validations: ValidationMap = {
  //   contactEmail: validateEmail,
  // };

  validations: ValidationMap = {};

  // تحويل قيمة isActive إلى قيمة منطقية (boolean)
  get isActiveBoolean(): boolean {
    return this.isActive === 1;
  }

  // الحصول على قائمة الوحدات المسموحة
  get allowedModulesList(): string[] {
    if (!this.allowedModules) return [];
    try {
      return JSON.parse(this.allowedModules);
    } catch {
      return [];
    }
  }

  // تعيين قائمة الوحدات المسموحة
  setAllowedModules(modules: string[]): void {
    this.allowedModules = JSON.stringify(modules);
  }

  // التحقق من صحة الترخيص - الكود الأصلي
  // isValid(): boolean {
  //   if (!this.isActiveBoolean) return false;
  //   if (!this.expiryDate) return false;
  //
  //   const now = new Date();
  //   const expiry = new Date(this.expiryDate);
  //
  //   return now <= expiry;
  // }

  // التحقق من صحة الترخيص - الكود المحدث مع تسجيل مفصل
  isValid(): boolean {
    console.log('=== التحقق من صحة الترخيص ===');
    console.log('حالة التفعيل:', this.isActiveBoolean);
    console.log('تاريخ الانتهاء:', this.expiryDate);

    if (!this.isActiveBoolean) {
      console.log('الترخيص غير مفعل');
      return false;
    }

    if (!this.expiryDate) {
      console.log('تاريخ الانتهاء غير محدد');
      return false;
    }

    const now = new Date();
    const expiry = new Date(this.expiryDate);
    const isValid = now <= expiry;

    console.log('التاريخ الحالي:', now.toISOString());
    console.log('تاريخ الانتهاء:', expiry.toISOString());
    console.log('الترخيص صالح:', isValid);
    console.log('=== انتهاء التحقق من صحة الترخيص ===');

    return isValid;
  }

  // التحقق من انتهاء صلاحية الترخيص
  isExpired(): boolean {
    if (!this.expiryDate) return true;
    
    const now = new Date();
    const expiry = new Date(this.expiryDate);
    
    return now > expiry;
  }

  // الحصول على عدد الأيام المتبقية
  getDaysRemaining(): number {
    if (!this.expiryDate) return 0;
    
    const now = new Date();
    const expiry = new Date(this.expiryDate);
    const diffTime = expiry.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return Math.max(0, diffDays);
  }

  // التحقق من الوحدة المسموحة
  isModuleAllowed(module: string): boolean {
    const allowedModules = this.allowedModulesList;
    return allowedModules.includes(module) || allowedModules.includes('all');
  }

  // إنشاء معرف الجهاز - الكود الأصلي
  // static generateMachineId(): string {
  //   const os = require('os');
  //   const crypto = require('crypto');
  //
  //   const networkInterfaces = os.networkInterfaces();
  //   const cpus = os.cpus();
  //   const platform = os.platform();
  //   const arch = os.arch();
  //
  //   // جمع معلومات فريدة عن الجهاز
  //   const machineInfo = {
  //     platform,
  //     arch,
  //     cpuModel: cpus[0]?.model || '',
  //     totalMemory: os.totalmem(),
  //     hostname: os.hostname(),
  //   };
  //
  //   // إضافة عناوين MAC للشبكة
  //   const macAddresses: string[] = [];
  //   Object.values(networkInterfaces).forEach(interfaces => {
  //     interfaces?.forEach(iface => {
  //       if (iface.mac && iface.mac !== '00:00:00:00:00:00') {
  //         macAddresses.push(iface.mac);
  //       }
  //     });
  //   });
  //
  //   machineInfo['macAddresses'] = macAddresses.sort();
  //
  //   // إنشاء hash فريد
  //   const hash = crypto.createHash('sha256');
  //   hash.update(JSON.stringify(machineInfo));
  //
  //   return hash.digest('hex').substring(0, 32);
  // }

  // إنشاء معرف الجهاز - الكود الجديد مع الرقم التسلسلي (مُصحح)
  static generateMachineId(): string {
    try {
      const os = require('os');
      const crypto = require('crypto');

      const networkInterfaces = os.networkInterfaces();
      const cpus = os.cpus();
      const platform = os.platform();
      const arch = os.arch();

      // جمع معلومات فريدة عن الجهاز
      const machineInfo: any = {
        platform,
        arch,
        cpuModel: cpus[0]?.model || '',
        totalMemory: os.totalmem(),
        hostname: os.hostname(),
      };

      // إضافة عناوين MAC للشبكة
      const macAddresses: string[] = [];
      if (networkInterfaces && typeof networkInterfaces === 'object') {
        Object.values(networkInterfaces).forEach((interfaces: any) => {
          if (Array.isArray(interfaces)) {
            interfaces.forEach((iface: any) => {
              if (iface && iface.mac && iface.mac !== '00:00:00:00:00:00') {
                macAddresses.push(iface.mac);
              }
            });
          }
        });
      }

      machineInfo.macAddresses = macAddresses.sort();

      // إنشاء hash فريد
      const hash = crypto.createHash('sha256');
      hash.update(JSON.stringify(machineInfo));

      return hash.digest('hex').substring(0, 32);
    } catch (error) {
      console.error('Error generating machine ID:', error);
      // إرجاع معرف افتراضي في حالة الخطأ
      return 'DEFAULT-MACHINE-ID-' + Date.now().toString(36);
    }
  }

  // إنشاء الرقم التسلسلي للعميل - دالة محدثة مع كلمة السر السرية (الكود الأصلي)
  // static generateSerialNumber(): string {
  //   try {
  //     const os = require('os');
  //     const crypto = require('crypto');

  //     const networkInterfaces = os.networkInterfaces();
  //     const cpus = os.cpus();
  //     const platform = os.platform();
  //     const arch = os.arch();

  //     // جمع معلومات الجهاز لإنشاء الرقم التسلسلي
  //     const machineInfo: any = {
  //       platform,
  //       arch,
  //       cpuModel: cpus[0]?.model || '',
  //       totalMemory: os.totalmem(),
  //       hostname: os.hostname(),
  //       userInfo: os.userInfo().username,
  //       secret: License.SECRET_KEY // إضافة كلمة السر السرية
  //     };

  //     // جمع عناوين MAC
  //     const macAddresses: string[] = [];
  //     if (networkInterfaces && typeof networkInterfaces === 'object') {
  //       Object.values(networkInterfaces).forEach((interfaces: any) => {
  //         if (Array.isArray(interfaces)) {
  //           interfaces.forEach((iface: any) => {
  //             if (iface && iface.mac && iface.mac !== '00:00:00:00:00:00') {
  //               macAddresses.push(iface.mac);
  //             }
  //           });
  //         }
  //       });
  //     }

  //     // أخذ أول عنوان MAC كمعرف أساسي
  //     const primaryMac = macAddresses.sort()[0] || 'NO-MAC';
  //     machineInfo.primaryMac = primaryMac;

  //     // إنشاء hash للمعلومات مع كلمة السر السرية
  //     const hash = crypto.createHash('sha256');
  //     hash.update(JSON.stringify(machineInfo));
  //     const fullHash = hash.digest('hex');

  //     // تنسيق الرقم التسلسلي: XXXX-XXXX-XXXX-XXXX
  //     const serialParts = [
  //       fullHash.substring(0, 4).toUpperCase(),
  //       fullHash.substring(4, 8).toUpperCase(),
  //       fullHash.substring(8, 12).toUpperCase(),
  //       fullHash.substring(12, 16).toUpperCase(),
  //     ];

  //     const serialNumber = serialParts.join('-');

  //     // تسجيل معلومات التشخيص (للمطور فقط)
  //     console.log('=== معلومات إنشاء الرقم التسلسلي ===');
  //     console.log('المنصة:', platform);
  //     console.log('المعمارية:', arch);
  //     console.log('اسم الجهاز:', os.hostname());
  //     console.log('المستخدم:', os.userInfo().username);
  //     console.log('عناوين MAC:', macAddresses);
  //     console.log('المعالج:', cpus[0]?.model || 'غير معروف');
  //     console.log('الذاكرة (GB):', Math.floor(os.totalmem() / (1024 * 1024 * 1024)));
  //     console.log('الرقم التسلسلي:', serialNumber);
  //     console.log('=====================================');

  //     return serialNumber;
  //   } catch (error) {
  //     console.error('Error generating serial number:', error);
  //     // إرجاع رقم تسلسلي افتراضي في حالة الخطأ مع كلمة السر السرية
  //     const crypto = require('crypto');
  //     const fallbackData = {
  //       timestamp: Date.now(),
  //       random: Math.random(),
  //       secret: License.SECRET_KEY
  //     };
  //     const hash = crypto.createHash('sha256');
  //     hash.update(JSON.stringify(fallbackData));
  //     const fallbackHash = hash.digest('hex');

  //     return `ERR-${fallbackHash.substring(0, 4).toUpperCase()}-${fallbackHash.substring(4, 8).toUpperCase()}-${fallbackHash.substring(8, 12).toUpperCase()}`;
  //   }
  // }

  // إنشاء الرقم التسلسلي للعميل - دالة بسيطة وفعالة
  static generateSerialNumber(): string {
    try {
      console.log('🔍 بدء إنشاء الرقم التسلسلي...');

      // محاولة استخدام معلومات النظام إذا كانت متوفرة
      let machineInfo: any = {};
      let useSystemInfo = false;

      // محاولة الوصول لمعلومات النظام
      try {
        // طريقة 1: require مباشرة
        if (typeof require !== 'undefined') {
          console.log('✅ محاولة استخدام require مباشرة...');
          const os = require('os');
          const crypto = require('crypto');

          machineInfo = {
            platform: os.platform(),
            arch: os.arch(),
            hostname: os.hostname(),
            userInfo: os.userInfo().username,
            totalMemory: os.totalmem(),
            secret: License.SECRET_KEY
            // إزالة timestamp لضمان ثبات الرقم التسلسلي
          };

          // جمع عناوين MAC
          const networkInterfaces = os.networkInterfaces();
          const macAddresses: string[] = [];
          if (networkInterfaces) {
            Object.values(networkInterfaces).forEach((interfaces: any) => {
              if (Array.isArray(interfaces)) {
                interfaces.forEach((iface: any) => {
                  if (iface && iface.mac && iface.mac !== '00:00:00:00:00:00') {
                    macAddresses.push(iface.mac);
                  }
                });
              }
            });
          }
          machineInfo.primaryMac = macAddresses.sort()[0] || 'NO-MAC';

          useSystemInfo = true;
          console.log('✅ تم الحصول على معلومات النظام بنجاح');
        }
      } catch (error) {
        console.warn('⚠️ فشل في الوصول لمعلومات النظام:', error);
      }

      // إذا فشل الوصول لمعلومات النظام، استخدم معلومات المتصفح
      if (!useSystemInfo) {
        console.log('🌐 استخدام معلومات المتصفح...');
        machineInfo = {
          userAgent: navigator.userAgent,
          language: navigator.language,
          platform: navigator.platform,
          hardwareConcurrency: navigator.hardwareConcurrency || 1,
          maxTouchPoints: navigator.maxTouchPoints || 0,
          cookieEnabled: navigator.cookieEnabled,
          // إزالة onLine و timestamp لضمان ثبات الرقم التسلسلي
          secret: License.SECRET_KEY
        };
      }

      // إنشاء hash بسيط ومضمون
      const dataString = JSON.stringify(machineInfo);
      let hash = 0;
      for (let i = 0; i < dataString.length; i++) {
        const char = dataString.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // تحويل إلى 32bit integer
      }

      // إنشاء hash إضافي للتأكد من الفرادة
      const additionalHash = Math.abs(hash * 31 + dataString.length * 17);
      const combinedHash = (hash >>> 0).toString(16) + (additionalHash >>> 0).toString(16);
      const paddedHash = combinedHash.padStart(16, '0').substring(0, 16);

      // تنسيق الرقم التسلسلي: XXXX-XXXX-XXXX-XXXX
      const serialParts = [
        paddedHash.substring(0, 4).toUpperCase(),
        paddedHash.substring(4, 8).toUpperCase(),
        paddedHash.substring(8, 12).toUpperCase(),
        paddedHash.substring(12, 16).toUpperCase(),
      ];

      const serialNumber = serialParts.join('-');

      console.log('=== معلومات إنشاء الرقم التسلسلي ===');
      console.log('نوع المعلومات:', useSystemInfo ? 'معلومات النظام' : 'معلومات المتصفح');
      console.log('البيانات المستخدمة:', Object.keys(machineInfo));
      console.log('الرقم التسلسلي:', serialNumber);
      console.log('=====================================');

      return serialNumber;

    } catch (error) {
      console.error('❌ خطأ في إنشاء الرقم التسلسلي:', error);

      // إنشاء رقم تسلسلي احتياطي بسيط وثابت
      const fallbackData = {
        userAgent: navigator.userAgent || 'unknown',
        platform: navigator.platform || 'unknown',
        language: navigator.language || 'en',
        secret: License.SECRET_KEY
        // إزالة timestamp و random لضمان ثبات الرقم التسلسلي
      };

      const dataString = JSON.stringify(fallbackData);
      let hash = 0;
      for (let i = 0; i < dataString.length; i++) {
        const char = dataString.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
      }

      const hashString = Math.abs(hash).toString(16).padStart(16, '0').substring(0, 16);
      const serialParts = [
        'FALL',
        hashString.substring(0, 4).toUpperCase(),
        hashString.substring(4, 8).toUpperCase(),
        hashString.substring(8, 12).toUpperCase(),
      ];

      const fallbackSerial = serialParts.join('-');
      console.log('🔄 تم إنشاء رقم تسلسلي احتياطي:', fallbackSerial);

      return fallbackSerial;
    }
  }

  // إنشاء رقم تسلسلي بناءً على معلومات المتصفح
  private static generateBrowserBasedSerial(): string {
    try {
      const browserInfo = {
        userAgent: navigator.userAgent,
        language: navigator.language,
        platform: navigator.platform,
        cookieEnabled: navigator.cookieEnabled,
        hardwareConcurrency: navigator.hardwareConcurrency || 1,
        maxTouchPoints: navigator.maxTouchPoints || 0,
        secret: License.SECRET_KEY
        // إزالة onLine لضمان ثبات الرقم التسلسلي
      };

      // استخدام Web Crypto API إذا كان متوفراً
      if (window.crypto && window.crypto.subtle) {
        const encoder = new TextEncoder();
        const data = encoder.encode(JSON.stringify(browserInfo));

        // إنشاء hash بسيط
        let hash = 0;
        for (let i = 0; i < data.length; i++) {
          const char = data[i];
          hash = ((hash << 5) - hash) + char;
          hash = hash & hash; // تحويل إلى 32bit integer
        }

        const hashString = Math.abs(hash).toString(16).padStart(16, '0');

        const serialParts = [
          hashString.substring(0, 4).toUpperCase(),
          hashString.substring(4, 8).toUpperCase(),
          hashString.substring(8, 12).toUpperCase(),
          hashString.substring(12, 16).toUpperCase(),
        ];

        const serialNumber = serialParts.join('-');

        console.log('=== معلومات إنشاء الرقم التسلسلي (متصفح) ===');
        console.log('وكيل المستخدم:', navigator.userAgent);
        console.log('المنصة:', navigator.platform);
        console.log('اللغة:', navigator.language);
        console.log('الرقم التسلسلي:', serialNumber);
        console.log('===============================================');

        return serialNumber;
      } else {
        return License.generateFallbackSerial();
      }
    } catch (error) {
      console.error('Error generating browser-based serial:', error);
      return License.generateFallbackSerial();
    }
  }

  // إنشاء رقم تسلسلي احتياطي ثابت
  private static generateFallbackSerial(): string {
    const fallbackData = {
      userAgent: navigator.userAgent || 'unknown',
      platform: navigator.platform || 'unknown',
      language: navigator.language || 'en',
      secret: License.SECRET_KEY
      // إزالة timestamp و random لضمان ثبات الرقم التسلسلي
    };

    // إنشاء hash بسيط
    const dataString = JSON.stringify(fallbackData);
    let hash = 0;
    for (let i = 0; i < dataString.length; i++) {
      const char = dataString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }

    const hashString = Math.abs(hash).toString(16).padStart(16, '0');

    const serialParts = [
      'FALL',
      hashString.substring(0, 4).toUpperCase(),
      hashString.substring(4, 8).toUpperCase(),
      hashString.substring(8, 12).toUpperCase(),
    ];

    const serialNumber = serialParts.join('-');

    console.log('=== رقم تسلسلي احتياطي ===');
    console.log('الرقم التسلسلي:', serialNumber);
    console.log('========================');

    return serialNumber;
  }

  // التحقق من الرقم التسلسلي مع معرف الجهاز - الكود الأصلي
  // static validateSerialNumber(serialNumber: string): boolean {
  //   const generatedSerial = License.generateSerialNumber();
  //   return generatedSerial === serialNumber;
  // }

  // التحقق من الرقم التسلسلي مع معرف الجهاز - الكود المحدث
  static validateSerialNumber(serialNumber: string): boolean {
    try {
      // التحقق من تنسيق الرقم التسلسلي
      if (!serialNumber || typeof serialNumber !== 'string') {
        console.error('Invalid serial number format');
        return false;
      }

      // التحقق من تنسيق الرقم (XXXX-XXXX-XXXX-XXXX أو ERR-XXXX-XXXX-XXXX)
      const serialPattern = /^[A-Z0-9]{3,4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/;
      if (!serialPattern.test(serialNumber)) {
        console.error('Serial number format invalid:', serialNumber);
        return false;
      }

      // إنشاء الرقم التسلسلي الحالي للجهاز
      const currentSerial = License.generateSerialNumber();

      // مقارنة الرقم المُدخل مع الرقم الحالي
      const isValid = currentSerial === serialNumber;

      if (!isValid) {
        console.log('Serial number mismatch:');
        console.log('Expected:', currentSerial);
        console.log('Provided:', serialNumber);
      }

      return isValid;
    } catch (error) {
      console.error('Error validating serial number:', error);
      return false;
    }
  }

  // التحقق من معرف الجهاز - الكود الأصلي
  // validateMachineId(): boolean {
  //   if (!this.machineId) return true; // إذا لم يتم تعيين معرف الجهاز، فالترخيص صالح
  //
  //   const currentMachineId = License.generateMachineId();
  //   return this.machineId === currentMachineId;
  // }

  // التحقق من معرف الجهاز - الكود المحدث مع تسجيل مفصل
  validateMachineId(): boolean {
    console.log('=== التحقق من معرف الجهاز ===');
    console.log('معرف الجهاز المحفوظ:', this.machineId);

    if (!this.machineId) {
      console.log('لا يوجد معرف جهاز محفوظ - الترخيص صالح');
      return true;
    }

    const currentMachineId = License.generateMachineId();
    console.log('معرف الجهاز الحالي:', currentMachineId);

    const isValid = this.machineId === currentMachineId;
    console.log('تطابق معرف الجهاز:', isValid);
    console.log('=== انتهاء التحقق من معرف الجهاز ===');

    return isValid;
  }

  // تحديث آخر تحقق
  async updateLastValidation(): Promise<void> {
    this.lastValidation = new Date();
    this.validationCount = (this.validationCount || 0) + 1;
    await this.sync();
  }

  // تفعيل الترخيص
  async activate(): Promise<void> {
    this.activationDate = new Date();
    this.machineId = License.generateMachineId();
    this.isActive = 1;
    await this.sync();
  }

  // إلغاء تفعيل الترخيص
  async deactivate(): Promise<void> {
    this.isActive = 0;
    await this.sync();
  }

  // كلمة السر السرية بين أداة الإنشاء والنظام الأساسي
  private static readonly SECRET_KEY = 'NEWSMART_LICENSE_SECRET_2024_MONEER_ALSHAWEA';

  // مفاتيح الترخيص الافتراضية - للاستخدام العام
  private static readonly DEFAULT_LICENSES = {
    // مفتاح ترخيص دائم افتراضي - يعمل مع أي جهاز
    PERMANENT_UNIVERSAL: 'PRM-UNIV-ERSA-L2024-PERM',

    // مفتاح ترخيص تجريبي افتراضي - يعمل مع أي جهاز لمدة 30 يوم
    TRIAL_UNIVERSAL: 'TRL-UNIV-ERSA-L2024-TEMP'
  };

  // إنشاء مفتاح ترخيص بناءً على الرقم التسلسلي - الكود الأصلي
  // static generateLicenseKey(
  //   serialNumber: string,
  //   licenseType: 'trial' | 'permanent' = 'trial',
  //   durationDays: number = 7
  // ): string {
  //   const crypto = require('crypto');

  //   // إزالة الشرطات من الرقم التسلسلي
  //   const cleanSerial = serialNumber.replace(/-/g, '');

  //   // إنشاء بيانات الترخيص
  //   const licenseData = {
  //     serial: cleanSerial,
  //     type: licenseType,
  //     duration: durationDays,
  //     timestamp: Date.now(),
  //   };

  //   // إنشاء hash للبيانات
  //   const hash = crypto.createHash('sha256');
  //   hash.update(JSON.stringify(licenseData));
  //   const licenseHash = hash.digest('hex');

  //   // تنسيق مفتاح الترخيص
  //   let prefix = '';
  //   switch (licenseType) {
  //     case 'trial':
  //       prefix = 'TRL';
  //       break;
  //     case 'permanent':
  //       prefix = 'PRM';
  //       break;
  //     default:
  //       prefix = 'GEN';
  //   }

  //   // تكوين المفتاح: PREFIX-XXXX-XXXX-XXXX-XXXX
  //   const keyParts = [
  //     prefix,
  //     licenseHash.substring(0, 4).toUpperCase(),
  //     licenseHash.substring(4, 8).toUpperCase(),
  //     licenseHash.substring(8, 12).toUpperCase(),
  //     licenseHash.substring(12, 16).toUpperCase(),
  //   ];

  //   return keyParts.join('-');
  // }

  // إنشاء مفتاح ترخيص بناءً على الرقم التسلسلي - الكود المحدث مع كلمة السر السرية
  static generateLicenseKey(
    serialNumber: string,
    licenseType: 'trial' | 'permanent' = 'trial',
    durationDays: number = 7
  ): string {
    // محاولة الحصول على crypto بطرق مختلفة
    let crypto: any = null;
    try {
      if (typeof require !== 'undefined') {
        crypto = require('crypto');
      } else if (typeof window !== 'undefined' && (window as any).require) {
        crypto = (window as any).require('crypto');
      } else if (typeof global !== 'undefined' && (global as any).require) {
        crypto = (global as any).require('crypto');
      }
    } catch (error) {
      console.error('Could not access crypto module:', error);
      throw new Error('Crypto module not available');
    }

    if (!crypto) {
      throw new Error('Crypto module not available');
    }

    // إزالة الشرطات من الرقم التسلسلي
    const cleanSerial = serialNumber.replace(/-/g, '');

    // إنشاء بيانات الترخيص مع كلمة السر السرية
    const licenseData = {
      serial: cleanSerial,
      type: licenseType,
      secret: License.SECRET_KEY
    };

    // إنشاء hash للبيانات مع كلمة السر السرية
    const hash = crypto.createHash('sha256');
    hash.update(JSON.stringify(licenseData));
    const licenseHash = hash.digest('hex');

    // تنسيق مفتاح الترخيص
    let prefix = '';
    switch (licenseType) {
      case 'trial':
        prefix = 'TRL';
        break;
      case 'permanent':
        prefix = 'PRM';
        break;
      default:
        prefix = 'GEN';
    }

    // تكوين المفتاح: PREFIX-XXXX-XXXX-XXXX-XXXX
    const keyParts = [
      prefix,
      licenseHash.substring(0, 4).toUpperCase(),
      licenseHash.substring(4, 8).toUpperCase(),
      licenseHash.substring(8, 12).toUpperCase(),
      licenseHash.substring(12, 16).toUpperCase(),
    ];

    return keyParts.join('-');
  }

  // التحقق من صحة مفتاح الترخيص مع الرقم التسلسلي - دالة محدثة مع دعم المفاتيح الافتراضية
  static validateLicenseKeyWithSerial(licenseKey: string, serialNumber: string): {
    isValid: boolean;
    licenseType: string;
    error?: string;
  } {
    try {
      console.log('=== بدء التحقق من مفتاح الترخيص ===');
      console.log('مفتاح الترخيص:', licenseKey);
      console.log('الرقم التسلسلي المُدخل:', serialNumber);

      // التحقق من المفاتيح الافتراضية أولاً - تجاهل التحقق من الرقم التسلسلي
      console.log('🔍 فحص المفاتيح الافتراضية...');
      console.log('مفتاح دائم افتراضي:', License.DEFAULT_LICENSES.PERMANENT_UNIVERSAL);
      console.log('مفتاح تجريبي افتراضي:', License.DEFAULT_LICENSES.TRIAL_UNIVERSAL);

      if (licenseKey === License.DEFAULT_LICENSES.PERMANENT_UNIVERSAL) {
        console.log('✅ تم التعرف على مفتاح الترخيص الدائم الافتراضي - تجاهل التحقق من الرقم التسلسلي');
        return { isValid: true, licenseType: 'permanent' };
      }

      if (licenseKey === License.DEFAULT_LICENSES.TRIAL_UNIVERSAL) {
        console.log('✅ تم التعرف على مفتاح الترخيص التجريبي الافتراضي - تجاهل التحقق من الرقم التسلسلي');
        return { isValid: true, licenseType: 'trial' };
      }

      console.log('❌ المفتاح ليس من المفاتيح الافتراضية، سيتم التحقق كمفتاح مخصص...');
      console.log('الرقم التسلسلي الحالي:', License.generateSerialNumber());
      // التحقق من تنسيق المفتاح
      const keyParts = licenseKey.split('-');
      if (keyParts.length !== 5) {
        return { isValid: false, licenseType: '', error: 'تنسيق مفتاح الترخيص غير صحيح' };
      }

      const [prefix, ...hashParts] = keyParts;

      // تحديد نوع الترخيص
      let licenseType = '';
      switch (prefix) {
        case 'TRL':
          licenseType = 'trial';
          break;
        case 'PRM':
          licenseType = 'permanent';
          break;
        default:
          return { isValid: false, licenseType: '', error: 'نوع الترخيص غير معروف' };
      }

      // التحقق من الرقم التسلسلي - الكود الأصلي
      // if (!License.validateSerialNumber(serialNumber)) {
      //   return { isValid: false, licenseType: '', error: 'الرقم التسلسلي غير صحيح لهذا الجهاز' };
      // }

      // التحقق من الرقم التسلسلي - الكود المحدث مع تسجيل مفصل
      console.log('🔍 بدء التحقق من الرقم التسلسلي للمفتاح المخصص...');
      const serialValidation = License.validateSerialNumber(serialNumber);
      console.log('نتيجة التحقق من الرقم التسلسلي:', serialValidation);

      if (!serialValidation) {
        console.error('❌ فشل التحقق من الرقم التسلسلي:');
        console.error('الرقم المُدخل:', serialNumber);
        console.error('الرقم الحالي للجهاز:', License.generateSerialNumber());
        console.error('السبب: الرقم التسلسلي لا يتطابق مع هذا الجهاز');
        return { isValid: false, licenseType: '', error: 'الرقم التسلسلي غير صحيح لهذا الجهاز' };
      }

      console.log('✅ تم التحقق من الرقم التسلسلي بنجاح');

      // إنشاء مفتاح متوقع للمقارنة مع كلمة السر السرية
      const expectedKey = License.generateLicenseKey(serialNumber, licenseType as any);
      const expectedHashParts = expectedKey.split('-').slice(1);

      // مقارنة أجزاء الـ hash مع تسجيل مفصل
      console.log('أجزاء المفتاح المتوقعة:', expectedHashParts);
      console.log('أجزاء المفتاح الفعلية:', hashParts);

      const isHashValid = hashParts.every((part, index) => {
        const match = part === expectedHashParts[index];
        if (!match) {
          console.log(`عدم تطابق في الجزء ${index}: متوقع=${expectedHashParts[index]}, فعلي=${part}`);
        }
        return match;
      });

      console.log('نتيجة التحقق:', isHashValid ? 'صحيح' : 'خاطئ');
      console.log('=== انتهاء التحقق من مفتاح الترخيص ===');

      if (!isHashValid) {
        return { isValid: false, licenseType: '', error: 'مفتاح الترخيص غير صحيح لهذا الجهاز' };
      }

      return { isValid: true, licenseType };

    } catch (error) {
      console.error('خطأ في التحقق من مفتاح الترخيص:', error);
      console.log('=== انتهاء التحقق من مفتاح الترخيص (خطأ) ===');
      return { isValid: false, licenseType: '', error: 'خطأ في التحقق من مفتاح الترخيص' };
    }
  }

  // دالة اختبار للتحقق من عمل الخوارزمية - للمطور فقط
  static testLicenseGeneration(serialNumber: string): {
    serialNumber: string;
    trialKey: string;
    permanentKey: string;
    testResults: {
      trialValid: boolean;
      permanentValid: boolean;
    };
  } {
    try {
      // إنشاء مفاتيح اختبار
      const trialKey = License.generateLicenseKey(serialNumber, 'trial');
      const permanentKey = License.generateLicenseKey(serialNumber, 'permanent');

      // اختبار التحقق من المفاتيح
      const trialValidation = License.validateLicenseKeyWithSerial(trialKey, serialNumber);
      const permanentValidation = License.validateLicenseKeyWithSerial(permanentKey, serialNumber);

      console.log('=== اختبار خوارزمية الترخيص ===');
      console.log('الرقم التسلسلي:', serialNumber);
      console.log('مفتاح تجريبي:', trialKey);
      console.log('مفتاح دائم:', permanentKey);
      console.log('نتيجة التحقق من المفتاح التجريبي:', trialValidation);
      console.log('نتيجة التحقق من المفتاح الدائم:', permanentValidation);

      return {
        serialNumber,
        trialKey,
        permanentKey,
        testResults: {
          trialValid: trialValidation.isValid,
          permanentValid: permanentValidation.isValid
        }
      };
    } catch (error) {
      console.error('خطأ في اختبار الخوارزمية:', error);
      throw error;
    }
  }

  // الحصول على المفاتيح الافتراضية - للمطور والاختبار
  static getDefaultLicenseKeys(): {
    permanentUniversal: string;
    trialUniversal: string;
    description: {
      permanent: string;
      trial: string;
    };
  } {
    return {
      permanentUniversal: License.DEFAULT_LICENSES.PERMANENT_UNIVERSAL,
      trialUniversal: License.DEFAULT_LICENSES.TRIAL_UNIVERSAL,
      description: {
        permanent: 'مفتاح ترخيص دائم يعمل مع أي جهاز - للاستخدام العام',
        trial: 'مفتاح ترخيص تجريبي يعمل مع أي جهاز لمدة 30 يوم - للاختبار'
      }
    };
  }

  // التحقق من كون المفتاح افتراضي
  static isDefaultLicenseKey(licenseKey: string): boolean {
    return licenseKey === License.DEFAULT_LICENSES.PERMANENT_UNIVERSAL ||
           licenseKey === License.DEFAULT_LICENSES.TRIAL_UNIVERSAL;
  }
}
