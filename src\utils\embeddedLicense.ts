// نظام الترخيص المدمج - للنسخ المخصصة للعملاء
// هذا الملف يحتوي على ترخيص مدمج في النظام لا يحتاج تفعيل

export interface EmbeddedLicenseConfig {
  isEmbedded: boolean;
  licenseType: 'permanent' | 'trial';
  expiryDate?: Date;
  maxUsers: number;
  allowedModules: string[];
  clientName: string;
  machineBinding: boolean;
  trialDays?: number;
}

// إعدادات الترخيص المدمج - يتم تعديلها حسب العميل
export const EMBEDDED_LICENSE_CONFIG: EmbeddedLicenseConfig = {
  // تفعيل الترخيص المدمج
  isEmbedded: false, // سيتم تغييرها إلى true عند البناء
  
  // نوع الترخيص - سيتم تحديده عند البناء
  licenseType: 'permanent', // أو 'trial'
  
  // تاريخ انتهاء الصلاحية للنسخة التجريبية
  expiryDate: undefined, // سيتم تحديده للنسخة التجريبية
  
  // عدد المستخدمين المسموح
  maxUsers: 50, // للنسخة الدائمة
  
  // الوحدات المسموحة
  allowedModules: ['all'], // جميع الوحدات للنسخة الدائمة
  
  // اسم العميل
  clientName: 'عميل مخصص',
  
  // ربط بالجهاز لمنع النقل
  machineBinding: true,
  
  // عدد أيام التجربة (للنسخة التجريبية فقط)
  trialDays: 30
};

// دالة التحقق من الترخيص المدمج
export function validateEmbeddedLicense(): {
  isValid: boolean;
  licenseType: string;
  daysRemaining?: number;
  error?: string;
} {
  try {
    console.log('🔍 التحقق من الترخيص المدمج...');
    
    // إذا لم يكن الترخيص مدمج، إرجاع false
    if (!EMBEDDED_LICENSE_CONFIG.isEmbedded) {
      console.log('❌ الترخيص غير مدمج');
      return { isValid: false, licenseType: '', error: 'الترخيص غير مدمج' };
    }
    
    // التحقق من ربط الجهاز
    if (EMBEDDED_LICENSE_CONFIG.machineBinding) {
      const currentMachineId = generateMachineFingerprint();
      const storedMachineId = localStorage.getItem('embedded_machine_id');
      
      if (!storedMachineId) {
        // أول تشغيل - حفظ معرف الجهاز
        localStorage.setItem('embedded_machine_id', currentMachineId);
        console.log('✅ تم ربط النظام بهذا الجهاز');
      } else if (storedMachineId !== currentMachineId) {
        // الجهاز مختلف - منع التشغيل
        console.error('❌ النظام مرتبط بجهاز آخر');
        return { 
          isValid: false, 
          licenseType: '', 
          error: 'هذا النظام مرتبط بجهاز آخر ولا يمكن تشغيله على هذا الجهاز' 
        };
      }
    }
    
    // التحقق من نوع الترخيص
    if (EMBEDDED_LICENSE_CONFIG.licenseType === 'permanent') {
      console.log('✅ ترخيص دائم مدمج صالح');
      return { 
        isValid: true, 
        licenseType: 'permanent' 
      };
    } else if (EMBEDDED_LICENSE_CONFIG.licenseType === 'trial') {
      // التحقق من انتهاء فترة التجربة
      const installDate = localStorage.getItem('embedded_install_date');
      const currentDate = new Date();
      
      if (!installDate) {
        // أول تشغيل - حفظ تاريخ التثبيت
        localStorage.setItem('embedded_install_date', currentDate.toISOString());
        console.log('✅ بدء فترة التجربة');
        return { 
          isValid: true, 
          licenseType: 'trial',
          daysRemaining: EMBEDDED_LICENSE_CONFIG.trialDays || 30
        };
      } else {
        // حساب الأيام المتبقية
        const installDateTime = new Date(installDate);
        const daysPassed = Math.floor((currentDate.getTime() - installDateTime.getTime()) / (1000 * 60 * 60 * 24));
        const daysRemaining = (EMBEDDED_LICENSE_CONFIG.trialDays || 30) - daysPassed;
        
        if (daysRemaining <= 0) {
          console.error('❌ انتهت فترة التجربة');
          return { 
            isValid: false, 
            licenseType: 'trial', 
            daysRemaining: 0,
            error: 'انتهت فترة التجربة' 
          };
        } else {
          console.log(`✅ فترة التجربة صالحة - ${daysRemaining} يوم متبقي`);

          // إضافة معلومات إضافية للتسجيل
          console.log(`📅 تاريخ التثبيت: ${installDate}`);
          console.log(`📅 التاريخ الحالي: ${currentDate.toISOString()}`);
          console.log(`📊 الأيام المنقضية: ${daysPassed}`);
          console.log(`⏰ إجمالي أيام التجربة: ${EMBEDDED_LICENSE_CONFIG.trialDays}`);

          return {
            isValid: true,
            licenseType: 'trial',
            daysRemaining: daysRemaining
          };
        }
      }
    }
    
    return { isValid: false, licenseType: '', error: 'نوع ترخيص غير معروف' };
    
  } catch (error) {
    console.error('❌ خطأ في التحقق من الترخيص المدمج:', error);
    return { isValid: false, licenseType: '', error: 'خطأ في التحقق من الترخيص' };
  }
}

// دالة إنشاء بصمة الجهاز
function generateMachineFingerprint(): string {
  try {
    // جمع معلومات الجهاز لإنشاء بصمة فريدة
    const fingerprint = {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      hardwareConcurrency: navigator.hardwareConcurrency || 1,
      maxTouchPoints: navigator.maxTouchPoints || 0,
      cookieEnabled: navigator.cookieEnabled,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      screen: {
        width: screen.width,
        height: screen.height,
        colorDepth: screen.colorDepth
      }
    };
    
    // إنشاء hash من المعلومات
    const dataString = JSON.stringify(fingerprint);
    let hash = 0;
    for (let i = 0; i < dataString.length; i++) {
      const char = dataString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // تحويل إلى 32bit integer
    }
    
    return Math.abs(hash).toString(16).toUpperCase();
  } catch (error) {
    console.error('خطأ في إنشاء بصمة الجهاز:', error);
    return 'UNKNOWN_MACHINE';
  }
}

// دالة التحقق من الوحدة المسموحة
export function isModuleAllowedEmbedded(module: string): boolean {
  if (!EMBEDDED_LICENSE_CONFIG.isEmbedded) {
    return false;
  }
  
  const allowedModules = EMBEDDED_LICENSE_CONFIG.allowedModules;
  return allowedModules.includes('all') || allowedModules.includes(module);
}

// دالة التحقق من عدد المستخدمين
export function checkUserLimitEmbedded(currentUserCount: number): boolean {
  if (!EMBEDDED_LICENSE_CONFIG.isEmbedded) {
    return false;
  }
  
  return currentUserCount <= EMBEDDED_LICENSE_CONFIG.maxUsers;
}

// دالة الحصول على معلومات الترخيص المدمج
export function getEmbeddedLicenseInfo(): {
  clientName: string;
  licenseType: string;
  maxUsers: number;
  allowedModules: string[];
  daysRemaining?: number;
} {
  const validation = validateEmbeddedLicense();

  return {
    clientName: EMBEDDED_LICENSE_CONFIG.clientName,
    licenseType: EMBEDDED_LICENSE_CONFIG.licenseType,
    maxUsers: EMBEDDED_LICENSE_CONFIG.maxUsers,
    allowedModules: EMBEDDED_LICENSE_CONFIG.allowedModules,
    daysRemaining: validation.daysRemaining
  };
}

// دالة للتحقق من ضرورة عرض إشعار الأيام المتبقية
export function shouldShowTrialNotification(): boolean {
  if (!EMBEDDED_LICENSE_CONFIG.isEmbedded || EMBEDDED_LICENSE_CONFIG.licenseType !== 'trial') {
    return false;
  }

  const lastNotificationDate = localStorage.getItem('last_trial_notification');
  const today = new Date().toDateString();

  // عرض الإشعار مرة واحدة يومياً
  if (lastNotificationDate !== today) {
    localStorage.setItem('last_trial_notification', today);
    return true;
  }

  return false;
}

// دالة للحصول على رسالة الإشعار المناسبة
export function getTrialNotificationMessage(daysRemaining: number): {
  title: string;
  message: string;
  urgency: 'low' | 'medium' | 'high';
} {
  let title: string;
  let urgency: 'low' | 'medium' | 'high';

  if (daysRemaining <= 3) {
    title = '⚠️ تحذير عاجل!';
    urgency = 'high';
  } else if (daysRemaining <= 7) {
    title = '⚠️ تحذير مهم!';
    urgency = 'medium';
  } else {
    title = 'ℹ️ معلومات النسخة التجريبية';
    urgency = 'low';
  }

  const message = `🔔 النسخة التجريبية - الأيام المتبقية: ${daysRemaining} يوم\n\n` +
                 `📅 انتهاء فترة التجربة خلال ${daysRemaining} ${daysRemaining === 1 ? 'يوم' : 'أيام'}\n` +
                 `💡 للحصول على النسخة الكاملة، يرجى التواصل مع الدعم الفني\n\n` +
                 `📞 الدعم الفني: <EMAIL>\n` +
                 `🌐 الموقع: www.newsmart.com`;

  return { title, message, urgency };
}
