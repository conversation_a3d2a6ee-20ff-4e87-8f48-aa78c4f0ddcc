   <  8  1  {"files":{"package.json":{"size":244,"integrity":{"algorithm":"SHA256","hash":"f619255b6752351867c0f8ba47c4123e1c169e5111dbfa86a21c2d38cdb3302a","blockSize":4194304,"blocks":["f619255b6752351867c0f8ba47c4123e1c169e5111dbfa86a21c2d38cdb3302a"]},"offset":"0"},"src":{"files":{"index.html":{"size":17220,"integrity":{"algorithm":"SHA256","hash":"3dc506bb01e55c5406d1d468a023fa6bf35ae360746e94fdf8c43b18aefb0072","blockSize":4194304,"blocks":["3dc506bb01e55c5406d1d468a023fa6bf35ae360746e94fdf8c43b18aefb0072"]},"offset":"244"},"main.js":{"size":13077,"integrity":{"algorithm":"SHA256","hash":"68defc8a15d4af2160c6540b155624a1bd2586b3371d93ffc1bd666d44c92374","blockSize":4194304,"blocks":["68defc8a15d4af2160c6540b155624a1bd2586b3371d93ffc1bd666d44c92374"]},"offset":"17464"},"renderer.js":{"size":15157,"integrity":{"algorithm":"SHA256","hash":"4de7e01be56761ef487ecd0deeb991d2fc138fcb88d3ca2004a2f4a7264d0a40","blockSize":4194304,"blocks":["4de7e01be56761ef487ecd0deeb991d2fc138fcb88d3ca2004a2f4a7264d0a40"]},"offset":"30541"}}},"node_modules":{"files":{"crypto":{"files":{"package.json":{"size":290,"integrity":{"algorithm":"SHA256","hash":"1fb6b1759cfefe87cc14ecfe454d50f99c24518971465153ad322919a9ce4fcb","blockSize":4194304,"blocks":["1fb6b1759cfefe87cc14ecfe454d50f99c24518971465153ad322919a9ce4fcb"]},"offset":"45698"}}}}}}}   {
  "name": "license-generator",
  "version": "1.0.0",
  "description": "أداة إنشاء مفاتيح الترخيص",
  "main": "src/main.js",
  "dependencies": {
    "crypto": "^1.0.1"
  },
  "author": "المطور",
  "license": "MIT"
}<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد التراخيص - أداة المطور</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .form-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid #e9ecef;
        }

        .form-section h2 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 1.4em;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }

        .form-group input[readonly] {
            background-color: #f8f9fa;
            color: #6c757d;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40,167,69,0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
        }

        .btn-copy {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
            padding: 8px 15px;
            font-size: 12px;
            margin-right: 10px;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .btn-delete {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            padding: 8px 15px;
            font-size: 12px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-delete:hover {
            background: linear-gradient(135deg, #c82333, #bd2130);
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
        }

        .result-section {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            border: 1px solid #c3e6cb;
            border-radius: 10px;
            padding: 25px;
            margin-top: 25px;
        }

        .result-section h3 {
            color: #155724;
            margin-bottom: 15px;
        }

        .license-key {
            background: white;
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            color: #155724;
            margin: 10px 0;
            word-break: break-all;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }

        .info-item {
            background: white;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }

        .info-item strong {
            color: #495057;
        }

        .history-section {
            margin-top: 30px;
        }

        .history-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .history-table th,
        .history-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #e9ecef;
        }

        .history-table th {
            background: linear-gradient(135deg, #495057, #343a40);
            color: white;
            font-weight: 600;
        }

        .history-table tr:hover {
            background-color: #f8f9fa;
        }

        .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .badge-trial {
            background-color: #fff3cd;
            color: #856404;
        }

        .badge-permanent {
            background-color: #d1ecf1;
            color: #0c5460;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .alert-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .alert-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .flex-row {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .flex-row input {
            flex: 1;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .content {
                padding: 20px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 مولد التراخيص</h1>
            <p>أداة المطور لإنشاء مفاتيح الترخيص</p>
        </div>

        <div class="content">
            <!-- قسم إنشاء الترخيص -->
            <div class="form-section">
                <h2>📝 إنشاء ترخيص جديد</h2>
                
                <div class="form-group">
                    <label for="serialNumber">الرقم التسلسلي للعميل:</label>
                    <div class="flex-row">
                        <input type="text" id="serialNumber" placeholder="XXXX-XXXX-XXXX-XXXX" required>
                        <button type="button" class="btn btn-secondary" onclick="getMySerial()">رقمي التسلسلي</button>
                    </div>
                </div>

                <div class="form-group">
                    <label for="licenseType">نوع الترخيص:</label>
                    <select id="licenseType" onchange="toggleDuration()">
                        <option value="trial">تجريبي</option>
                        <option value="permanent">دائم</option>
                    </select>
                </div>

                <div class="form-group" id="durationGroup">
                    <label for="duration">مدة الترخيص (بالأيام):</label>
                    <input type="number" id="duration" value="7" min="1" max="365">
                </div>

                <div class="form-group">
                    <label for="clientName">اسم العميل (اختياري):</label>
                    <input type="text" id="clientName" placeholder="اسم الشركة أو العميل">
                </div>

                <div class="form-group">
                    <label for="notes">ملاحظات (اختياري):</label>
                    <textarea id="notes" rows="3" placeholder="ملاحظات حول الترخيص"></textarea>
                </div>

                <div class="flex-row">
                    <button type="button" class="btn btn-primary" onclick="generateLicense()">
                        🔑 إنشاء مفتاح الترخيص
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="testLicenseAlgorithm()">
                        🧪 اختبار الخوارزمية
                    </button>
                </div>
            </div>

            <!-- قسم المفاتيح الافتراضية -->
            <div class="form-section" style="background: linear-gradient(135deg, #e8f5e8, #d4edda);">
                <h2 style="color: #155724; border-bottom-color: #28a745;">🔑 المفاتيح الافتراضية</h2>
                <p style="color: #155724; margin-bottom: 20px;">
                    هذه المفاتيح تعمل مع أي جهاز ولا تحتاج لرقم تسلسلي محدد:
                </p>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                    <!-- مفتاح دائم افتراضي -->
                    <div style="background: white; border: 2px solid #28a745; border-radius: 10px; padding: 15px;">
                        <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 10px;">
                            <h4 style="color: #155724; margin: 0;">ترخيص دائم (عام)</h4>
                            <button type="button" class="btn btn-copy" onclick="copyDefaultKey('permanent')" style="margin-right: 10px;">
                                📋 نسخ
                            </button>
                        </div>
                        <div class="license-key" style="font-size: 14px; border-color: #28a745;">
                            PRM-UNIV-ERSA-L2024-PERM
                        </div>
                        <div style="font-size: 12px; color: #6c757d; margin-top: 8px;">
                            ✅ ترخيص دائم غير محدود<br>
                            ✅ 50 مستخدم<br>
                            ✅ جميع الوحدات متاحة
                        </div>
                    </div>

                    <!-- مفتاح تجريبي افتراضي -->
                    <div style="background: white; border: 2px solid #ffc107; border-radius: 10px; padding: 15px;">
                        <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 10px;">
                            <h4 style="color: #856404; margin: 0;">ترخيص تجريبي (30 يوم)</h4>
                            <button type="button" class="btn btn-copy" onclick="copyDefaultKey('trial')" style="margin-right: 10px;">
                                📋 نسخ
                            </button>
                        </div>
                        <div class="license-key" style="font-size: 14px; border-color: #ffc107; color: #856404;">
                            TRL-UNIV-ERSA-L2024-TEMP
                        </div>
                        <div style="font-size: 12px; color: #6c757d; margin-top: 8px;">
                            ✅ ترخيص تجريبي لمدة 30 يوم<br>
                            ✅ 3 مستخدمين<br>
                            ✅ جميع الوحدات للاختبار
                        </div>
                    </div>
                </div>

                <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin-top: 20px;">
                    <p style="color: #856404; margin: 0; font-size: 14px;">
                        💡 <strong>ملاحظة:</strong> هذه المفاتيح مخصصة للاستخدام العام والاختبار.
                        يمكن استخدامها مع أي جهاز دون الحاجة لرقم تسلسلي محدد.
                    </p>
                </div>
            </div>

            <!-- قسم التحميل -->
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>جاري إنشاء مفتاح الترخيص...</p>
            </div>

            <!-- قسم النتيجة -->
            <div class="result-section" id="resultSection" style="display: none;">
                <h3>✅ تم إنشاء مفتاح الترخيص بنجاح</h3>
                
                <div class="form-group">
                    <label>مفتاح الترخيص:</label>
                    <div class="flex-row">
                        <div class="license-key" id="licenseKey"></div>
                        <button type="button" class="btn btn-copy" onclick="copyLicenseKey()">📋 نسخ</button>
                    </div>
                </div>

                <div class="info-grid" id="licenseInfo"></div>
            </div>

            <!-- قسم السجل -->
            <div class="history-section">
                <div class="form-section">
                    <h2>📊 سجل التراخيص المُنشأة</h2>
                    <div style="margin-bottom: 15px;">
                        <button type="button" class="btn btn-secondary" onclick="refreshHistory()">🔄 تحديث</button>
                        <button type="button" class="btn btn-success" onclick="exportHistory()">📤 تصدير</button>
                    </div>
                    <div style="overflow-x: auto;">
                        <table class="history-table">
                            <thead>
                                <tr>
                                    <th>النوع</th>
                                    <th>العميل</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>نسخ</th>
                                    <th>حذف</th>
                                </tr>
                            </thead>
                            <tbody id="historyTableBody">
                                <!-- سيتم ملء البيانات هنا -->
                            </tbody>
                        </table>

                        <!-- زر عرض الكل -->
                        <div style="text-align: center; margin-top: 15px;">
                            <button type="button" id="showAllButton" class="btn" onclick="toggleShowAll()" style="display: none; background: linear-gradient(135deg, #6c757d, #495057); color: white;">
                                عرض الكل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="renderer.js"></script>
</body>
</html>
const { app, BrowserWindow, ipcMain, dialog, clipboard } = require('electron');
const path = require('path');
const fs = require('fs');
const os = require('os');
const crypto = require('crypto');

// متغيرات عامة
let mainWindow;
let licenseHistory = [];

// تعطيل تحذيرات الأمان في وضع التطوير
process.env['ELECTRON_DISABLE_SECURITY_WARNINGS'] = 'true';

// إنشاء النافذة الرئيسية
function createWindow() {
  mainWindow = new BrowserWindow({
    width: 900,
    height: 700,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    icon: path.join(__dirname, '../assets/icon.png'),
    title: 'مولد التراخيص - أداة المطور',
    resizable: true,
    minimizable: true,
    maximizable: true,
    show: false
  });

  // تحميل الصفحة الرئيسية
  mainWindow.loadFile(path.join(__dirname, 'index.html'));

  // إظهار النافذة عند الانتهاء من التحميل
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // تحميل سجل التراخيص
    loadLicenseHistory();
  });

  // إغلاق التطبيق عند إغلاق النافذة
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // فتح أدوات المطور في وضع التطوير
  if (process.argv.includes('--dev')) {
    mainWindow.webContents.openDevTools();
  }
}

// كلمة السر السرية - يجب أن تطابق النظام الأساسي تماماً
const SECRET_KEY = 'NEWSMART_LICENSE_SECRET_2024_MONEER_ALSHAWEA';

// دالة إنشاء الرقم التسلسلي - محدثة مع كلمة السر السرية
function generateSerialNumber() {
  try {
    const networkInterfaces = os.networkInterfaces();
    const cpus = os.cpus();
    const platform = os.platform();
    const arch = os.arch();

    // جمع معلومات الجهاز مع كلمة السر السرية
    const machineInfo = {
      platform,
      arch,
      cpuModel: cpus[0]?.model || '',
      totalMemory: os.totalmem(),
      hostname: os.hostname(),
      userInfo: os.userInfo().username,
      secret: SECRET_KEY // إضافة كلمة السر السرية
    };

    // جمع عناوين MAC
    const macAddresses = [];
    if (networkInterfaces && typeof networkInterfaces === 'object') {
      Object.values(networkInterfaces).forEach((interfaces) => {
        if (Array.isArray(interfaces)) {
          interfaces.forEach((iface) => {
            if (iface && iface.mac && iface.mac !== '00:00:00:00:00:00') {
              macAddresses.push(iface.mac);
            }
          });
        }
      });
    }

    // أخذ أول عنوان MAC كمعرف أساسي
    const primaryMac = macAddresses.sort()[0] || 'NO-MAC';
    machineInfo.primaryMac = primaryMac;

    // إنشاء hash للمعلومات مع كلمة السر السرية
    const hash = crypto.createHash('sha256');
    hash.update(JSON.stringify(machineInfo));
    const fullHash = hash.digest('hex');

    // تنسيق الرقم التسلسلي: XXXX-XXXX-XXXX-XXXX
    const serialParts = [
      fullHash.substring(0, 4).toUpperCase(),
      fullHash.substring(4, 8).toUpperCase(),
      fullHash.substring(8, 12).toUpperCase(),
      fullHash.substring(12, 16).toUpperCase(),
    ];

    const serialNumber = serialParts.join('-');

    // تسجيل معلومات التشخيص
    console.log('=== معلومات إنشاء الرقم التسلسلي ===');
    console.log('المنصة:', platform);
    console.log('المعمارية:', arch);
    console.log('اسم الجهاز:', os.hostname());
    console.log('المستخدم:', os.userInfo().username);
    console.log('عناوين MAC:', macAddresses);
    console.log('المعالج:', cpus[0]?.model || 'غير معروف');
    console.log('الذاكرة (GB):', Math.floor(os.totalmem() / (1024 * 1024 * 1024)));
    console.log('الرقم التسلسلي:', serialNumber);
    console.log('=====================================');

    return serialNumber;
  } catch (error) {
    console.error('Error generating serial number:', error);
    // إرجاع رقم تسلسلي افتراضي في حالة الخطأ مع كلمة السر السرية
    const fallbackData = {
      timestamp: Date.now(),
      random: Math.random(),
      secret: SECRET_KEY
    };
    const hash = crypto.createHash('sha256');
    hash.update(JSON.stringify(fallbackData));
    const fallbackHash = hash.digest('hex');

    return `ERR-${fallbackHash.substring(0, 4).toUpperCase()}-${fallbackHash.substring(4, 8).toUpperCase()}-${fallbackHash.substring(8, 12).toUpperCase()}`;
  }
}

// دالة إنشاء مفتاح الترخيص - الكود الأصلي
// function generateLicenseKey(serialNumber, licenseType = 'trial', durationDays = 7) {
//   try {
//     // إزالة الشرطات من الرقم التسلسلي
//     const cleanSerial = serialNumber.replace(/-/g, '');
//
//     // إنشاء بيانات الترخيص
//     const licenseData = {
//       serial: cleanSerial,
//       type: licenseType,
//       duration: durationDays,
//       timestamp: Date.now(),
//     };
//
//     // إنشاء hash للبيانات
//     const hash = crypto.createHash('sha256');
//     hash.update(JSON.stringify(licenseData));
//     const licenseHash = hash.digest('hex');
//
//     // تنسيق مفتاح الترخيص
//     let prefix = '';
//     switch (licenseType) {
//       case 'trial':
//         prefix = 'TRL';
//         break;
//       case 'permanent':
//         prefix = 'PRM';
//         break;
//       default:
//         prefix = 'GEN';
//     }
//
//     // تكوين المفتاح: PREFIX-XXXX-XXXX-XXXX-XXXX
//     const keyParts = [
//       prefix,
//       licenseHash.substring(0, 4).toUpperCase(),
//       licenseHash.substring(4, 8).toUpperCase(),
//       licenseHash.substring(8, 12).toUpperCase(),
//       licenseHash.substring(12, 16).toUpperCase(),
//     ];
//
//     return keyParts.join('-');
//   } catch (error) {
//     console.error('Error generating license key:', error);
//     throw new Error('فشل في إنشاء مفتاح الترخيص');
//   }
// }

// دالة إنشاء مفتاح الترخيص - الكود المحدث مع كلمة السر السرية
function generateLicenseKey(serialNumber, licenseType = 'trial', durationDays = 7) {
  try {
    // إزالة الشرطات من الرقم التسلسلي
    const cleanSerial = serialNumber.replace(/-/g, '');

    // إنشاء بيانات الترخيص مع كلمة السر السرية (مطابق للنظام الأساسي)
    const licenseData = {
      serial: cleanSerial,
      type: licenseType,
      secret: SECRET_KEY // إضافة كلمة السر السرية
    };

    // إنشاء hash للبيانات مع كلمة السر السرية
    const hash = crypto.createHash('sha256');
    hash.update(JSON.stringify(licenseData));
    const licenseHash = hash.digest('hex');

    // تنسيق مفتاح الترخيص
    let prefix = '';
    switch (licenseType) {
      case 'trial':
        prefix = 'TRL';
        break;
      case 'permanent':
        prefix = 'PRM';
        break;
      default:
        prefix = 'GEN';
    }

    // تكوين المفتاح: PREFIX-XXXX-XXXX-XXXX-XXXX
    const keyParts = [
      prefix,
      licenseHash.substring(0, 4).toUpperCase(),
      licenseHash.substring(4, 8).toUpperCase(),
      licenseHash.substring(8, 12).toUpperCase(),
      licenseHash.substring(12, 16).toUpperCase(),
    ];

    const licenseKey = keyParts.join('-');

    // تسجيل معلومات التشخيص
    console.log('=== معلومات إنشاء مفتاح الترخيص ===');
    console.log('الرقم التسلسلي:', serialNumber);
    console.log('الرقم المنظف:', cleanSerial);
    console.log('نوع الترخيص:', licenseType);
    console.log('مفتاح الترخيص:', licenseKey);
    console.log('==========================================');

    return licenseKey;
  } catch (error) {
    console.error('Error generating license key:', error);
    throw new Error('فشل في إنشاء مفتاح الترخيص');
  }
}

// دالة تحميل سجل التراخيص
function loadLicenseHistory() {
  try {
    const historyPath = path.join(os.homedir(), 'license-generator-history.json');
    if (fs.existsSync(historyPath)) {
      const data = fs.readFileSync(historyPath, 'utf8');
      licenseHistory = JSON.parse(data);
    }
  } catch (error) {
    console.error('Error loading license history:', error);
    licenseHistory = [];
  }
}

// دالة حفظ سجل التراخيص
function saveLicenseHistory() {
  try {
    const historyPath = path.join(os.homedir(), 'license-generator-history.json');
    // الاحتفاظ بآخر 100 ترخيص فقط
    const historyToSave = licenseHistory.slice(0, 100);
    fs.writeFileSync(historyPath, JSON.stringify(historyToSave, null, 2));
  } catch (error) {
    console.error('Error saving license history:', error);
  }
}

// معالجات IPC
// دالة اختبار للتحقق من عمل الخوارزمية - للمطور فقط
function testLicenseGeneration(serialNumber) {
  try {
    // إنشاء مفاتيح اختبار
    const trialKey = generateLicenseKey(serialNumber, 'trial');
    const permanentKey = generateLicenseKey(serialNumber, 'permanent');

    console.log('=== اختبار خوارزمية الترخيص في أداة الإنشاء ===');
    console.log('الرقم التسلسلي:', serialNumber);
    console.log('مفتاح تجريبي:', trialKey);
    console.log('مفتاح دائم:', permanentKey);

    return {
      serialNumber,
      trialKey,
      permanentKey
    };
  } catch (error) {
    console.error('خطأ في اختبار الخوارزمية:', error);
    throw error;
  }
}

ipcMain.handle('get-serial-number', () => {
  return generateSerialNumber();
});

ipcMain.handle('test-license-generation', (event, serialNumber) => {
  try {
    return { success: true, result: testLicenseGeneration(serialNumber) };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('generate-license', (event, data) => {
  try {
    const { serialNumber, licenseType, duration, clientName, notes } = data;
    
    // إنشاء مفتاح الترخيص
    const licenseKey = generateLicenseKey(serialNumber, licenseType, duration);
    
    // إنشاء كائن الترخيص
    const license = {
      key: licenseKey,
      serialNumber: serialNumber,
      type: licenseType,
      duration: duration,
      clientName: clientName || '',
      notes: notes || '',
      createdAt: new Date().toISOString(),
    };
    
    // إضافة إلى السجل
    licenseHistory.unshift(license);
    saveLicenseHistory();
    
    return { success: true, license };
  } catch (error) {
    console.error('Error generating license:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-license-history', () => {
  return licenseHistory;
});

ipcMain.handle('copy-to-clipboard', (event, text) => {
  try {
    clipboard.writeText(text);
    return { success: true };
  } catch (error) {
    console.error('Error copying to clipboard:', error);
    return { success: false, error: error.message };
  }
});

// معالج حفظ سجل التراخيص
ipcMain.handle('save-license-history', async (event, history) => {
  try {
    licenseHistory = history; // تحديث المتغير العام
    const historyPath = path.join(app.getPath('userData'), 'license-history.json');
    await fs.writeFile(historyPath, JSON.stringify(history, null, 2), 'utf8');
    return { success: true };
  } catch (error) {
    console.error('Error saving license history:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('export-history', async () => {
  try {
    const result = await dialog.showSaveDialog(mainWindow, {
      title: 'تصدير سجل التراخيص',
      defaultPath: 'license-history.json',
      filters: [
        { name: 'JSON Files', extensions: ['json'] },
        { name: 'All Files', extensions: ['*'] }
      ]
    });
    
    if (!result.canceled && result.filePath) {
      fs.writeFileSync(result.filePath, JSON.stringify(licenseHistory, null, 2));
      return { success: true, path: result.filePath };
    }
    
    return { success: false, error: 'تم إلغاء العملية' };
  } catch (error) {
    console.error('Error exporting history:', error);
    return { success: false, error: error.message };
  }
});

// أحداث التطبيق
app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
const { ipcRenderer } = require('electron');

// متغيرات عامة
let currentLicense = null;
let licenseHistory = [];

// تحميل البيانات عند بدء التطبيق
document.addEventListener('DOMContentLoaded', async () => {
    await refreshHistory();
    showAlert('مرحباً بك في مولد التراخيص! 🎉', 'success');
});

// دالة تبديل عرض مدة الترخيص
function toggleDuration() {
    const licenseType = document.getElementById('licenseType').value;
    const durationGroup = document.getElementById('durationGroup');
    
    if (licenseType === 'permanent') {
        durationGroup.style.display = 'none';
    } else {
        durationGroup.style.display = 'block';
    }
}

// دالة الحصول على الرقم التسلسلي للجهاز الحالي
async function getMySerial() {
    try {
        const serialNumber = await ipcRenderer.invoke('get-serial-number');
        document.getElementById('serialNumber').value = serialNumber;
        showAlert('تم الحصول على الرقم التسلسلي للجهاز الحالي', 'success');
    } catch (error) {
        console.error('Error getting serial number:', error);
        showAlert('حدث خطأ أثناء الحصول على الرقم التسلسلي', 'error');
    }
}

// دالة اختبار خوارزمية الترخيص
async function testLicenseAlgorithm() {
    const serialNumber = document.getElementById('serialNumber').value.trim();

    if (!serialNumber) {
        showAlert('يرجى إدخال الرقم التسلسلي أولاً', 'error');
        return;
    }

    try {
        showLoading(true);
        const result = await ipcRenderer.invoke('test-license-generation', serialNumber);

        if (result.success) {
            const { trialKey, permanentKey } = result.result;

            // عرض النتائج في نافذة منبثقة
            const testResults = `
=== نتائج اختبار خوارزمية الترخيص ===

الرقم التسلسلي: ${serialNumber}

المفتاح التجريبي: ${trialKey}
المفتاح الدائم: ${permanentKey}

تم إنشاء المفاتيح بنجاح! ✅
يمكنك الآن استخدام هذه المفاتيح لاختبار النظام الأساسي.
            `;

            alert(testResults);
            showAlert('تم اختبار الخوارزمية بنجاح! ✅', 'success');

            // نسخ المفتاح الدائم تلقائياً للاختبار
            await ipcRenderer.invoke('copy-to-clipboard', permanentKey);
            showAlert('تم نسخ المفتاح الدائم للحافظة 📋', 'info');

        } else {
            showAlert(`فشل في الاختبار: ${result.error}`, 'error');
        }
    } catch (error) {
        console.error('Error testing license algorithm:', error);
        showAlert('حدث خطأ أثناء اختبار الخوارزمية', 'error');
    } finally {
        showLoading(false);
    }
}

// دالة إنشاء مفتاح الترخيص
async function generateLicense() {
    const serialNumber = document.getElementById('serialNumber').value.trim();
    const licenseType = document.getElementById('licenseType').value;
    const duration = parseInt(document.getElementById('duration').value) || 7;
    const clientName = document.getElementById('clientName').value.trim();
    const notes = document.getElementById('notes').value.trim();

    // التحقق من صحة البيانات
    if (!serialNumber) {
        showAlert('يرجى إدخال الرقم التسلسلي', 'error');
        return;
    }

    if (serialNumber.length < 10) {
        showAlert('الرقم التسلسلي غير صحيح', 'error');
        return;
    }

    // إظهار شاشة التحميل
    showLoading(true);
    hideResult();

    try {
        const result = await ipcRenderer.invoke('generate-license', {
            serialNumber,
            licenseType,
            duration,
            clientName,
            notes
        });

        if (result.success) {
            currentLicense = result.license;
            showResult(result.license);
            await refreshHistory();
            showAlert('تم إنشاء مفتاح الترخيص بنجاح! 🎉', 'success');
            
            // مسح النموذج
            clearForm();
        } else {
            showAlert(`حدث خطأ: ${result.error}`, 'error');
        }
    } catch (error) {
        console.error('Error generating license:', error);
        showAlert('حدث خطأ أثناء إنشاء مفتاح الترخيص', 'error');
    } finally {
        showLoading(false);
    }
}

// دالة عرض النتيجة
function showResult(license) {
    const resultSection = document.getElementById('resultSection');
    const licenseKeyElement = document.getElementById('licenseKey');
    const licenseInfoElement = document.getElementById('licenseInfo');

    licenseKeyElement.textContent = license.key;

    // عرض معلومات الترخيص
    const licenseTypeText = license.type === 'trial' ? 'تجريبي' : 'دائم';
    const createdAt = new Date(license.createdAt).toLocaleString('ar-SA');

    licenseInfoElement.innerHTML = `
        <div class="info-item">
            <strong>النوع:</strong> ${licenseTypeText}
        </div>
        <div class="info-item">
            <strong>الرقم التسلسلي:</strong> ${license.serialNumber}
        </div>
        <div class="info-item">
            <strong>تاريخ الإنشاء:</strong> ${createdAt}
        </div>
        ${license.type === 'trial' ? `
        <div class="info-item">
            <strong>المدة:</strong> ${license.duration} يوم
        </div>
        ` : ''}
        ${license.clientName ? `
        <div class="info-item">
            <strong>العميل:</strong> ${license.clientName}
        </div>
        ` : ''}
        ${license.notes ? `
        <div class="info-item">
            <strong>الملاحظات:</strong> ${license.notes}
        </div>
        ` : ''}
    `;

    resultSection.style.display = 'block';
    resultSection.scrollIntoView({ behavior: 'smooth' });
}

// دالة إخفاء النتيجة
function hideResult() {
    document.getElementById('resultSection').style.display = 'none';
}

// دالة نسخ مفتاح الترخيص
async function copyLicenseKey() {
    if (!currentLicense) return;

    try {
        const result = await ipcRenderer.invoke('copy-to-clipboard', currentLicense.key);
        if (result.success) {
            showAlert('تم نسخ مفتاح الترخيص! 📋', 'success');
        } else {
            showAlert('فشل في نسخ مفتاح الترخيص', 'error');
        }
    } catch (error) {
        console.error('Error copying license key:', error);
        showAlert('حدث خطأ أثناء النسخ', 'error');
    }
}

// دالة نسخ مفتاح من السجل
async function copyHistoryKey(key) {
    try {
        const result = await ipcRenderer.invoke('copy-to-clipboard', key);
        if (result.success) {
            showAlert('تم نسخ مفتاح الترخيص! 📋', 'success');
        } else {
            showAlert('فشل في نسخ مفتاح الترخيص', 'error');
        }
    } catch (error) {
        console.error('Error copying license key:', error);
        showAlert('حدث خطأ أثناء النسخ', 'error');
    }
}

// دالة نسخ المفاتيح الافتراضية
async function copyDefaultKey(type) {
    const defaultKeys = {
        permanent: 'PRM-UNIV-ERSA-L2024-PERM',
        trial: 'TRL-UNIV-ERSA-L2024-TEMP'
    };

    const key = defaultKeys[type];
    if (!key) {
        showAlert('مفتاح غير صحيح', 'error');
        return;
    }

    try {
        const result = await ipcRenderer.invoke('copy-to-clipboard', key);
        if (result.success) {
            const keyType = type === 'permanent' ? 'الدائم' : 'التجريبي';
            showAlert(`تم نسخ المفتاح ${keyType} الافتراضي! 📋`, 'success');
        } else {
            showAlert('فشل في نسخ المفتاح الافتراضي', 'error');
        }
    } catch (error) {
        console.error('Error copying default key:', error);
        showAlert('حدث خطأ أثناء النسخ', 'error');
    }
}

// دالة حذف ترخيص
async function deleteLicenseKey(index) {
    if (index < 0 || index >= licenseHistory.length) {
        showAlert('فهرس الترخيص غير صحيح', 'error');
        return;
    }

    const license = licenseHistory[index];
    const licenseTypeText = license.type === 'trial' ? 'التجريبي' : 'الدائم';
    const clientName = license.clientName || 'غير محدد';

    // تأكيد الحذف
    const confirmDelete = confirm(`هل أنت متأكد من حذف الترخيص ${licenseTypeText} للعميل "${clientName}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`);

    if (!confirmDelete) {
        return;
    }

    try {
        // حذف الترخيص من المصفوفة
        licenseHistory.splice(index, 1);

        // حفظ التحديث
        const result = await ipcRenderer.invoke('save-license-history', licenseHistory);

        if (result.success) {
            showAlert(`تم حذف الترخيص ${licenseTypeText} بنجاح! 🗑️`, 'success');

            // تحديث الجدول
            updateHistoryTable();
        } else {
            showAlert('فشل في حفظ التحديث', 'error');
            // إعادة إضافة الترخيص في حالة فشل الحفظ
            licenseHistory.splice(index, 0, license);
        }
    } catch (error) {
        console.error('Error deleting license:', error);
        showAlert('حدث خطأ أثناء حذف الترخيص', 'error');
        // إعادة إضافة الترخيص في حالة حدوث خطأ
        licenseHistory.splice(index, 0, license);
    }
}

// دالة تبديل عرض جميع التراخيص
function toggleShowAll() {
    showAllLicenses = !showAllLicenses;
    updateHistoryTable();
}

// دالة تحديث السجل
async function refreshHistory() {
    try {
        licenseHistory = await ipcRenderer.invoke('get-license-history');
        updateHistoryTable();
    } catch (error) {
        console.error('Error refreshing history:', error);
        showAlert('حدث خطأ أثناء تحديث السجل', 'error');
    }
}

// متغير لتتبع عرض جميع التراخيص
let showAllLicenses = false;

// دالة تحديث جدول السجل
function updateHistoryTable() {
    const tbody = document.getElementById('historyTableBody');
    const showAllButton = document.getElementById('showAllButton');

    if (licenseHistory.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="5" style="text-align: center; color: #6c757d; padding: 30px;">
                    لا يوجد تراخيص مُنشأة بعد
                </td>
            </tr>
        `;
        if (showAllButton) showAllButton.style.display = 'none';
        return;
    }

    // تحديد التراخيص المراد عرضها
    const licensesToShow = showAllLicenses ? licenseHistory : licenseHistory.slice(0, 5);

    // إظهار/إخفاء زر "عرض الكل"
    if (showAllButton) {
        if (licenseHistory.length > 5) {
            showAllButton.style.display = 'block';
            showAllButton.textContent = showAllLicenses ? 'عرض أول 5 فقط' : `عرض الكل (${licenseHistory.length})`;
        } else {
            showAllButton.style.display = 'none';
        }
    }

    tbody.innerHTML = licensesToShow.map((license, index) => {
        const licenseTypeText = license.type === 'trial' ? 'تجريبي' : 'دائم';
        const badgeClass = license.type === 'trial' ? 'badge-trial' : 'badge-permanent';
        const createdAt = new Date(license.createdAt).toLocaleString('ar-SA');
        const clientName = license.clientName || 'غير محدد';
        const actualIndex = showAllLicenses ? index : index; // الفهرس الحقيقي في المصفوفة

        return `
            <tr id="license-row-${actualIndex}">
                <td>
                    <span class="badge ${badgeClass}">${licenseTypeText}</span>
                </td>
                <td>${clientName}</td>
                <td>${createdAt}</td>
                <td>
                    <button type="button" class="btn btn-copy" onclick="copyHistoryKey('${license.key}')">
                        📋 نسخ المفتاح
                    </button>
                </td>
                <td>
                    <button type="button" class="btn btn-delete" onclick="deleteLicenseKey(${actualIndex})" title="حذف هذا الترخيص">
                        🗑️ حذف
                    </button>
                </td>
            </tr>
        `;
    }).join('');
}

// دالة تصدير السجل
async function exportHistory() {
    try {
        const result = await ipcRenderer.invoke('export-history');
        if (result.success) {
            showAlert(`تم تصدير السجل بنجاح إلى: ${result.path}`, 'success');
        } else {
            showAlert(`فشل في تصدير السجل: ${result.error}`, 'error');
        }
    } catch (error) {
        console.error('Error exporting history:', error);
        showAlert('حدث خطأ أثناء تصدير السجل', 'error');
    }
}

// دالة إظهار/إخفاء شاشة التحميل
function showLoading(show) {
    const loading = document.getElementById('loading');
    loading.style.display = show ? 'block' : 'none';
}

// دالة عرض التنبيهات
function showAlert(message, type) {
    // إزالة التنبيهات السابقة
    const existingAlerts = document.querySelectorAll('.alert');
    existingAlerts.forEach(alert => alert.remove());

    // إنشاء تنبيه جديد
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.textContent = message;

    // إضافة التنبيه في بداية المحتوى
    const content = document.querySelector('.content');
    content.insertBefore(alert, content.firstChild);

    // إزالة التنبيه بعد 5 ثوان
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 5000);
}

// دالة مسح النموذج
function clearForm() {
    document.getElementById('serialNumber').value = '';
    document.getElementById('licenseType').value = 'trial';
    document.getElementById('duration').value = '7';
    document.getElementById('clientName').value = '';
    document.getElementById('notes').value = '';
    toggleDuration();
}

// إضافة مستمعي الأحداث
document.getElementById('licenseType').addEventListener('change', toggleDuration);

// تهيئة الواجهة
toggleDuration();
{
  "name": "crypto",
  "version": "1.0.1",
  "description": "",
  "main": "index.js",
  "repository": {
    "type": "git",
    "url": "git+https://github.com/npm/deprecate-holder.git"
  },
  "author": "",
  "license": "ISC",
  "homepage": "https://github.com/npm/deprecate-holder#readme"
}