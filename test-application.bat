@echo off
title اختبار تطبيق newsmart المحاسبي
color 0B
chcp 65001 >nul

echo ========================================
echo    اختبار تطبيق newsmart المحاسبي
echo ========================================
echo.

set TEST_PASSED=0
set TEST_FAILED=0
set TEST_WARNINGS=0

echo بدء الاختبارات الشاملة...
echo.

echo [1/8] اختبار وجود الملفات الأساسية...
call :test_basic_files
echo.

echo [2/8] اختبار أحجام الملفات...
call :test_file_sizes
echo.

echo [3/8] اختبار التبعيات...
call :test_dependencies
echo.

echo [4/8] اختبار قاعدة البيانات...
call :test_database
echo.

echo [5/8] اختبار واجهة المستخدم...
call :test_ui
echo.

echo [6/8] اختبار الأمان...
call :test_security
echo.

echo [7/8] اختبار الأداء...
call :test_performance
echo.

echo [8/8] اختبار التوافق...
call :test_compatibility
echo.

echo ========================================
echo نتائج الاختبار النهائية
echo ========================================
echo.
echo ✅ اختبارات نجحت: %TEST_PASSED%
echo ❌ اختبارات فشلت: %TEST_FAILED%
echo ⚠️ تحذيرات: %TEST_WARNINGS%
echo.

if %TEST_FAILED% GTR 0 (
    echo ❌ فشل في بعض الاختبارات! يرجى مراجعة الأخطاء أعلاه.
    echo.
    pause
    exit /b 1
) else (
    echo ✅ جميع الاختبارات نجحت! التطبيق جاهز للتوزيع.
    echo.
)

echo هل تريد إنشاء تقرير مفصل؟ (Y/N)
set /p choice=
if /i "%choice%"=="Y" call :generate_report

pause
goto end

:test_basic_files
echo فحص الملفات الأساسية...

if exist "dist\newsmart Setup *.exe" (
    echo ✅ ملف المثبت موجود
    set /a TEST_PASSED+=1
) else (
    echo ❌ ملف المثبت غير موجود
    set /a TEST_FAILED+=1
)

if exist "package.json" (
    echo ✅ ملف package.json موجود
    set /a TEST_PASSED+=1
) else (
    echo ❌ ملف package.json غير موجود
    set /a TEST_FAILED+=1
)

if exist "main.ts" (
    echo ✅ ملف main.ts موجود
    set /a TEST_PASSED+=1
) else (
    echo ❌ ملف main.ts غير موجود
    set /a TEST_FAILED+=1
)

if exist "src\App.vue" (
    echo ✅ ملف App.vue موجود
    set /a TEST_PASSED+=1
) else (
    echo ❌ ملف App.vue غير موجود
    set /a TEST_FAILED+=1
)

exit /b 0

:test_file_sizes
echo فحص أحجام الملفات...

for %%f in ("dist\newsmart Setup *.exe") do (
    if %%~zf LSS 20000000 (
        echo ⚠️ تحذير: حجم المثبت صغير جداً (أقل من 20MB)
        set /a TEST_WARNINGS+=1
    ) else if %%~zf GTR 200000000 (
        echo ⚠️ تحذير: حجم المثبت كبير جداً (أكثر من 200MB)
        set /a TEST_WARNINGS+=1
    ) else (
        echo ✅ حجم المثبت مناسب (%%~zf bytes)
        set /a TEST_PASSED+=1
    )
)

if exist "node_modules" (
    for /f %%i in ('dir /s /b node_modules 2^>nul ^| find /c /v ""') do (
        if %%i GTR 50000 (
            echo ⚠️ تحذير: عدد ملفات node_modules كبير جداً (%%i ملف)
            set /a TEST_WARNINGS+=1
        ) else (
            echo ✅ عدد ملفات node_modules مقبول (%%i ملف)
            set /a TEST_PASSED+=1
        )
    )
)

exit /b 0

:test_dependencies
echo فحص التبعيات...

npm list --depth=0 >nul 2>&1
if errorlevel 1 (
    echo ❌ مشاكل في التبعيات
    set /a TEST_FAILED+=1
) else (
    echo ✅ جميع التبعيات سليمة
    set /a TEST_PASSED+=1
)

npm audit --audit-level=high >nul 2>&1
if errorlevel 1 (
    echo ⚠️ تحذير: ثغرات أمنية في التبعيات
    set /a TEST_WARNINGS+=1
) else (
    echo ✅ لا توجد ثغرات أمنية خطيرة
    set /a TEST_PASSED+=1
)

exit /b 0

:test_database
echo فحص قاعدة البيانات...

if exist "schemas" (
    echo ✅ مجلد schemas موجود
    set /a TEST_PASSED+=1
    
    for %%f in (schemas\*.json) do (
        echo فحص ملف: %%f
        REM يمكن إضافة فحص JSON هنا
    )
) else (
    echo ❌ مجلد schemas غير موجود
    set /a TEST_FAILED+=1
)

if exist "models" (
    echo ✅ مجلد models موجود
    set /a TEST_PASSED+=1
) else (
    echo ❌ مجلد models غير موجود
    set /a TEST_FAILED+=1
)

exit /b 0

:test_ui
echo فحص واجهة المستخدم...

if exist "src\components" (
    echo ✅ مجلد components موجود
    set /a TEST_PASSED+=1
) else (
    echo ❌ مجلد components غير موجود
    set /a TEST_FAILED+=1
)

if exist "src\pages" (
    echo ✅ مجلد pages موجود
    set /a TEST_PASSED+=1
) else (
    echo ❌ مجلد pages غير موجود
    set /a TEST_FAILED+=1
)

if exist "src\styles" (
    echo ✅ مجلد styles موجود
    set /a TEST_PASSED+=1
) else (
    echo ⚠️ تحذير: مجلد styles غير موجود
    set /a TEST_WARNINGS+=1
)

exit /b 0

:test_security
echo فحص الأمان...

findstr /i "password" src\*.* >nul 2>&1
if not errorlevel 1 (
    echo ⚠️ تحذير: كلمات مرور محتملة في الكود
    set /a TEST_WARNINGS+=1
) else (
    echo ✅ لا توجد كلمات مرور مكشوفة
    set /a TEST_PASSED+=1
)

findstr /i "api_key\|secret\|token" src\*.* >nul 2>&1
if not errorlevel 1 (
    echo ⚠️ تحذير: مفاتيح API محتملة في الكود
    set /a TEST_WARNINGS+=1
) else (
    echo ✅ لا توجد مفاتيح API مكشوفة
    set /a TEST_PASSED+=1
)

exit /b 0

:test_performance
echo فحص الأداء...

if exist "dist" (
    for /f %%i in ('dir /s /b dist 2^>nul ^| find /c /v ""') do (
        if %%i GTR 1000 (
            echo ⚠️ تحذير: عدد ملفات dist كبير (%%i ملف)
            set /a TEST_WARNINGS+=1
        ) else (
            echo ✅ عدد ملفات dist مقبول (%%i ملف)
            set /a TEST_PASSED+=1
        )
    )
) else (
    echo ❌ مجلد dist غير موجود
    set /a TEST_FAILED+=1
)

REM فحص حجم ملفات JavaScript
for %%f in ("dist\*.js") do (
    if %%~zf GTR 5000000 (
        echo ⚠️ تحذير: ملف JS كبير: %%f (%%~zf bytes)
        set /a TEST_WARNINGS+=1
    )
)

exit /b 0

:test_compatibility
echo فحص التوافق...

node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير متوفر
    set /a TEST_FAILED+=1
) else (
    for /f "tokens=1 delims=v" %%i in ('node --version') do (
        echo ✅ Node.js متوفر: %%i
        set /a TEST_PASSED+=1
    )
)

npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm غير متوفر
    set /a TEST_FAILED+=1
) else (
    for /f %%i in ('npm --version') do (
        echo ✅ npm متوفر: %%i
        set /a TEST_PASSED+=1
    )
)

exit /b 0

:generate_report
echo إنشاء تقرير مفصل...

set REPORT_FILE=test-report-%date:~-4,4%%date:~-10,2%%date:~-7,2%-%time:~0,2%%time:~3,2%.txt

(
echo تقرير اختبار تطبيق newsmart المحاسبي
echo =====================================
echo.
echo تاريخ الاختبار: %date%
echo وقت الاختبار: %time%
echo.
echo النتائج:
echo ✅ اختبارات نجحت: %TEST_PASSED%
echo ❌ اختبارات فشلت: %TEST_FAILED%
echo ⚠️ تحذيرات: %TEST_WARNINGS%
echo.
echo تفاصيل النظام:
echo نظام التشغيل: %OS%
echo المعالج: %PROCESSOR_ARCHITECTURE%
echo المستخدم: %USERNAME%
echo.
) > "%REPORT_FILE%"

echo ✅ تم إنشاء التقرير: %REPORT_FILE%

exit /b 0

:end
echo.
echo شكراً لاستخدام أداة الاختبار!
echo.
