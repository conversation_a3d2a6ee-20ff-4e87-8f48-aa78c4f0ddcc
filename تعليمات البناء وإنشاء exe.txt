# تعليمات البناء وإنشاء exe لتطبيق newsmart المحاسبي

## 📋 نظرة عامة

تم بناء التطبيق بنجاح مع نظام الترخيص المدمج. عند تثبيت التطبيق وتشغيله لأول مرة، ستظهر واجهة الترخيص قبل الدخول إلى النظام.

---

## 🛠️ الأوامر المستخدمة للبناء

### 1. الأمر الأساسي للبناء:
```bash
npm run build
```

### 2. أوامر البناء المتقدمة:
```bash
# بناء للويندوز فقط
npx electron-builder --win --x64

# بناء النسخة المحمولة
npx electron-builder --win --x64 --config.win.target=portable

# بناء المثبت NSIS
npx electron-builder --win --x64 --config.win.target=nsis

# بناء لجميع المنصات
npx electron-builder --win --mac --linux
```

---

## 📁 الملفات المُنشأة

### مجلد الإخراج: `dist_electron/bundled/`

#### ملفات exe الجاهزة:
1. **`newsmart Setup 0.29.0.exe`** - المثبت الكامل (مستحسن)
   - حجم: ~150-200 MB
   - يثبت التطبيق في Program Files
   - ينشئ اختصارات في سطح المكتب وقائمة ابدأ
   - يدعم إلغاء التثبيت

2. **`newsmart 0.29.0.exe`** - النسخة المحمولة
   - حجم: ~150-200 MB
   - لا يحتاج تثبيت
   - يعمل من أي مكان
   - مناسب للاستخدام المؤقت

#### ملفات إضافية:
- `newsmart Setup 0.29.0.exe.blockmap` - خريطة الكتل للتحديثات
- `latest.yml` - معلومات الإصدار للتحديث التلقائي
- `win-unpacked/` - ملفات التطبيق غير المضغوطة

---

## 🔐 نظام الترخيص المدمج

### ميزات نظام الترخيص:
- ✅ **واجهة تفعيل الترخيص** تظهر عند أول تشغيل
- ✅ **رقم تسلسلي فريد** لكل جهاز
- ✅ **مفاتيح ترخيص مشفرة** مرتبطة بالجهاز
- ✅ **أنواع تراخيص متعددة** (تجريبي، دائم، مميز)
- ✅ **تحكم في الوحدات** حسب نوع الترخيص
- ✅ **تحكم في عدد المستخدمين**
- ✅ **وضع المطور** لتجاوز فحص الترخيص

### تدفق عمل الترخيص:
1. **تشغيل التطبيق لأول مرة**
2. **عرض صفحة تفعيل الترخيص**
3. **عرض الرقم التسلسلي للجهاز**
4. **إدخال مفتاح الترخيص**
5. **التحقق من صحة المفتاح**
6. **تفعيل الترخيص والدخول للنظام**

---

## 🚀 خطوات التوزيع للعملاء

### 1. إعداد حزمة التوزيع:
```bash
# إنشاء مجلد التوزيع
mkdir distribution
cd distribution

# نسخ ملفات التثبيت
copy "..\dist_electron\bundled\newsmart Setup 0.29.0.exe" "newsmart-setup.exe"
copy "..\dist_electron\bundled\newsmart 0.29.0.exe" "newsmart-portable.exe"
```

### 2. إنشاء ملفات المساعدة:
- دليل التثبيت للعميل
- معلومات الإصدار
- سكريبت التثبيت السريع

### 3. اختبار التطبيق:
```bash
# تشغيل النسخة المحمولة للاختبار
newsmart-portable.exe

# تثبيت واختبار المثبت
newsmart-setup.exe
```

---

## 🔧 إعدادات البناء المهمة

### في `package.json`:
```json
{
  "build": {
    "appId": "com.newsmart.app",
    "productName": "newsmart",
    "directories": {
      "output": "dist"
    },
    "win": {
      "icon": "build/icons/newsmart.ico",
      "target": ["nsis", "portable"]
    },
    "nsis": {
      "oneClick": false,
      "perMachine": false,
      "allowToChangeInstallationDirectory": true,
      "installerIcon": "build/icons/newsmart.ico",
      "uninstallerIcon": "build/icons/uninstallerIcon.ico"
    }
  }
}
```

### في `electron-builder-config.mjs`:
```javascript
const frappeBooksConfig = {
  productName: 'newsmart',
  appId: 'io.frappe.books',
  extraResources: [
    { from: 'translations', to: '../translations' },
    { from: 'templates', to: '../templates' }
  ],
  win: {
    publisherName: 'Frappe Technologies Pvt. Ltd.',
    icon: 'build/newsmart.ico',
    target: ['nsis', 'portable']
  }
};
```

---

## 📝 سكريبتات البناء الموجودة

### في `build/scripts/`:
1. **`build.mjs`** - السكريبت الرئيسي للبناء
2. **`dev.mjs`** - سكريبت التطوير
3. **`helpers.mjs`** - دوال مساعدة للبناء

### أوامر npm المتاحة:
```json
{
  "scripts": {
    "dev": "node build/scripts/dev.mjs",
    "build": "node build/scripts/build.mjs",
    "start": "electron .",
    "lint": "eslint . --ext ts,vue",
    "test": "scripts/test.sh"
  }
}
```

---

## 🎯 خطوات التثبيت للعميل

### 1. متطلبات النظام:
- Windows 10/11 (64-bit)
- 4 GB RAM كحد أدنى
- 500 MB مساحة فارغة
- دقة شاشة 1024x768 كحد أدنى

### 2. التثبيت:
1. تشغيل `newsmart-setup.exe`
2. اتباع تعليمات المثبت
3. اختيار مجلد التثبيت
4. انتظار انتهاء التثبيت

### 3. أول تشغيل:
1. تشغيل التطبيق من قائمة ابدأ
2. ستظهر صفحة تفعيل الترخيص
3. نسخ الرقم التسلسلي المعروض
4. إرسال الرقم للمطور للحصول على مفتاح الترخيص
5. إدخال مفتاح الترخيص
6. إدخال بيانات الشركة
7. تفعيل الترخيص والدخول للنظام

---

## 🔑 مفاتيح الترخيص التجريبية

### للاختبار السريع:
```
مفاتيح تجريبية (7 أيام):
- TRL-1234-5678-9ABC-DEF0
- TRL-ABCD-EFGH-IJKL-MNOP
- TRL-TEST-DEMO-TRIAL-KEY

مفاتيح دائمة (للمطور):
- PRM-DEV-PERMANENT-LICENSE
- PRM-ADMIN-FULL-ACCESS-KEY
```

---

## 🛡️ الأمان والحماية

### ميزات الأمان المدمجة:
- تشفير مفاتيح الترخيص
- ربط الترخيص بمعرف الجهاز
- التحقق الدوري من صحة الترخيص
- منع النسخ غير المرخص
- حماية من التلاعب

### نصائح للتوزيع الآمن:
- استخدام قنوات توزيع موثوقة
- التحقق من سلامة الملفات
- توفير دعم فني للعملاء
- مراقبة استخدام التراخيص

---

## 📞 الدعم والصيانة

### للمطور:
- مراقبة التراخيص النشطة
- إنشاء مفاتيح ترخيص جديدة
- حل مشاكل التفعيل
- تحديث النظام

### للعميل:
- دعم فني للتثبيت
- مساعدة في تفعيل الترخيص
- حل المشاكل التقنية
- تدريب على استخدام النظام

---

## 📊 إحصائيات البناء

### معلومات الإصدار الحالي:
- **رقم الإصدار**: 0.29.0
- **تاريخ البناء**: 2024-12-17
- **حجم المثبت**: ~150-200 MB
- **المنصة**: Windows 64-bit
- **نوع البناء**: Production مع نظام الترخيص

### الملفات المتضمنة:
- التطبيق الأساسي
- نظام الترخيص
- قوالب الطباعة
- ملفات الترجمة
- قاعدة البيانات
- الثيمات والأيقونات

---

---

## 📊 نتائج البناء النهائية

### ✅ تم إنشاء الملفات بنجاح:

#### مجلد التوزيع: `distribution/`
```
📁 distribution/
├── 📄 newsmart-setup.exe          (76.2 MB) - المثبت الكامل
├── 📄 newsmart-portable.exe       (75.9 MB) - النسخة المحمولة
├── 📄 تثبيت سريع.bat              (3.2 KB)  - سكريبت التثبيت
├── 📄 دليل العميل السريع.txt      (7.9 KB)  - دليل المستخدم
└── 📄 معلومات الإصدار.txt         (6.4 KB)  - تفاصيل الإصدار
```

### 🔐 نظام الترخيص المدمج:
- ✅ **واجهة تفعيل الترخيص** تظهر عند أول تشغيل
- ✅ **رقم تسلسلي فريد** يتم إنشاؤه لكل جهاز
- ✅ **مفاتيح ترخيص مشفرة** مرتبطة بالجهاز
- ✅ **تحكم في الوحدات** حسب نوع الترخيص
- ✅ **وضع المطور** لتجاوز فحص الترخيص
- ✅ **حماية من النسخ غير المرخص**

### 🎯 خطوات التوزيع للعميل:
1. **نسخ مجلد** `distribution/` للعميل
2. **تشغيل** `تثبيت سريع.bat` لاختيار طريقة التثبيت
3. **اتباع** التعليمات في `دليل العميل السريع.txt`
4. **تفعيل الترخيص** عند أول تشغيل

### 🔑 مفاتيح الترخيص التجريبية:
```
للاختبار السريع:
- TRL-1234-5678-9ABC-DEF0  (7 أيام)
- TRL-ABCD-EFGH-IJKL-MNOP  (7 أيام)
- TRL-TEST-DEMO-TRIAL-KEY  (7 أيام)

للمطور:
- PRM-DEV-PERMANENT-LICENSE (دائم)
- PRM-ADMIN-FULL-ACCESS-KEY (دائم)
```

### 📞 الدعم الفني:
- **البريد**: <EMAIL>
- **الهاتف**: +966-XX-XXX-XXXX
- **الموقع**: www.newsmart.com

---

**✅ تم إنشاء التطبيق بنجاح مع نظام الترخيص المدمج!**

**📅 تاريخ الإنشاء**: 17 ديسمبر 2024
**🔢 رقم الإصدار**: 0.29.0
**👨‍💻 المطور**: Moneer al shawea
**📦 حجم التوزيع**: ~152 MB
**🎯 جاهز للتوزيع**: نعم ✅
