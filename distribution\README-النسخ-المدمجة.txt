# 📦 دليل النسخ المدمجة الترخيص - نظام نيوسمارت المحاسبي

## 🎯 نظرة عامة

تم إنشاء نسختين مدمجتين الترخيص من نظام نيوسمارت المحاسبي:
- **النسخة الدائمة**: ترخيص دائم مدمج بدون انتهاء صلاحية
- **النسخة التجريبية**: ترخيص تجريبي لمدة 30 يوم

## ✨ مميزات النسخ المدمجة

### 🔒 **أمان وحماية:**
- ✅ **لا تحتاج مفتاح ترخيص** - الترخيص مدمج في النظام
- ✅ **مرتبطة بالجهاز** - لا يمكن نقلها لجهاز آخر
- ✅ **حماية من النسخ** - تعمل فقط على الجهاز المثبتة عليه
- ✅ **تشفير البيانات** - بصمة الجهاز مشفرة

### 🚀 **سهولة الاستخدام:**
- ✅ **تثبيت مباشر** - بدون خطوات تفعيل معقدة
- ✅ **بدء فوري** - تشغيل النظام مباشرة بعد التثبيت
- ✅ **لا توجد قيود شبكة** - تعمل بدون اتصال إنترنت
- ✅ **واجهة مبسطة** - بدون صفحات تفعيل

---

## 📋 تفاصيل النسخ

### 🟢 **النسخة الدائمة**
**الملفات:**
- `newsmart-permanent-embedded.exe` - نسخة التثبيت
- `newsmart-permanent-portable.exe` - النسخة المحمولة

**المواصفات:**
- ✅ **الترخيص**: دائم بدون انتهاء صلاحية
- ✅ **المستخدمين**: حتى 50 مستخدم
- ✅ **الوحدات**: جميع الوحدات متاحة
- ✅ **المدة**: غير محدودة
- ✅ **التحديثات**: مدعومة

### 🟡 **النسخة التجريبية**
**الملفات:**
- `newsmart-trial-30days.exe` - نسخة التثبيت
- `newsmart-trial-portable.exe` - النسخة المحمولة

**المواصفات:**
- ⏰ **الترخيص**: تجريبي لمدة 30 يوم
- 👥 **المستخدمين**: حتى 3 مستخدمين
- 📦 **الوحدات**: المبيعات، المخزون، العام
- ⏳ **المدة**: 30 يوم من أول تشغيل
- 🔔 **التنبيهات**: تحذير عند اقتراب انتهاء المدة

---

## 🛠️ طريقة البناء

### **للمطورين:**

#### **بناء النسخة الدائمة:**
```bash
npm run build:permanent
```

#### **بناء النسخة التجريبية:**
```bash
npm run build:trial
```

### **ما يحدث أثناء البناء:**
1. 📝 **تحديث إعدادات الترخيص** في `embeddedLicense.ts`
2. 🔨 **بناء النظام** مع الإعدادات الجديدة
3. 📦 **إنشاء ملفات exe** للتوزيع
4. 🔄 **استعادة الإعدادات الأصلية** تلقائياً

---

## 🔧 آلية العمل

### **عند أول تشغيل:**
1. 🔍 **فحص نوع الترخيص** (مدمج أم عادي)
2. 🆔 **إنشاء بصمة الجهاز** من معلومات الأجهزة
3. 💾 **حفظ بصمة الجهاز** في التخزين المحلي
4. ⏰ **تسجيل تاريخ التثبيت** (للنسخة التجريبية)
5. ✅ **بدء النظام** مباشرة

### **عند كل تشغيل:**
1. 🔍 **التحقق من بصمة الجهاز** المحفوظة
2. 🆔 **مقارنة مع بصمة الجهاز الحالية**
3. ⏰ **فحص انتهاء المدة** (للنسخة التجريبية)
4. ✅ **السماح بالدخول** أو ❌ **منع التشغيل**

### **حماية من النقل:**
- 🚫 **فشل التشغيل** عند تغيير الجهاز
- 🔒 **رسالة خطأ واضحة** عن ربط النظام بجهاز آخر
- 🛡️ **عدم إمكانية تجاوز الحماية** بطرق عادية

---

## 📊 مقارنة النسخ

| الميزة | النسخة العادية | النسخة الدائمة المدمجة | النسخة التجريبية المدمجة |
|--------|----------------|----------------------|--------------------------|
| **مفتاح الترخيص** | ✅ مطلوب | ❌ غير مطلوب | ❌ غير مطلوب |
| **صفحة التفعيل** | ✅ موجودة | ❌ مخفية | ❌ مخفية |
| **ربط بالجهاز** | ⚠️ اختياري | ✅ إجباري | ✅ إجباري |
| **المدة** | حسب المفتاح | ♾️ دائمة | ⏰ 30 يوم |
| **المستخدمين** | حسب المفتاح | 👥 50 مستخدم | 👥 3 مستخدمين |
| **الوحدات** | حسب المفتاح | 📦 جميع الوحدات | 📦 محدودة |
| **النقل** | ✅ ممكن | ❌ مستحيل | ❌ مستحيل |

---

## 🎯 حالات الاستخدام

### **النسخة الدائمة مناسبة لـ:**
- 🏢 **الشركات الكبيرة** التي تريد حلول دائمة
- 🔒 **البيئات الآمنة** التي تتطلب حماية عالية
- 💼 **العملاء المميزين** الذين يدفعون رسوم كاملة
- 🏭 **المؤسسات** التي تحتاج جميع الوحدات

### **النسخة التجريبية مناسبة لـ:**
- 🧪 **العملاء المحتملين** لتجربة النظام
- 📊 **العروض التوضيحية** والمؤتمرات
- 🎓 **التدريب** والتعليم
- 🔍 **اختبار الميزات** قبل الشراء

---

## ⚠️ تحذيرات مهمة

### **للمطورين:**
- 🔄 **لا تنس استعادة الإعدادات** بعد البناء
- 💾 **احتفظ بنسخة احتياطية** من الكود الأصلي
- 🧪 **اختبر النسخ** قبل التوزيع
- 📝 **وثق التغييرات** لكل عميل

### **للمستخدمين:**
- 💻 **لا تنقل النظام** لجهاز آخر
- 🔄 **لا تغير مكونات الجهاز** الأساسية
- ⏰ **راقب تاريخ انتهاء التجربة**
- 💾 **احتفظ بنسخة احتياطية** من البيانات

### **للعملاء:**
- 📞 **تواصل مع الدعم** عند أي مشاكل
- 🔄 **لا تحاول نقل النظام** بنفسك
- 📋 **اقرأ شروط الاستخدام** بعناية
- 💳 **ادفع الرسوم** للحصول على النسخة الدائمة

---

## 🆘 استكشاف الأخطاء

### **خطأ: "النظام مرتبط بجهاز آخر"**
- 🔍 **السبب**: تم نقل النظام لجهاز مختلف
- 🛠️ **الحل**: إعادة تثبيت النظام على الجهاز الأصلي
- 📞 **البديل**: التواصل مع الدعم الفني

### **خطأ: "انتهت فترة التجربة"**
- 🔍 **السبب**: مرور 30 يوم على النسخة التجريبية
- 🛠️ **الحل**: شراء النسخة الدائمة
- 📞 **البديل**: طلب تمديد فترة التجربة

### **خطأ: "خطأ في التحقق من الترخيص"**
- 🔍 **السبب**: مشكلة في ملفات النظام
- 🛠️ **الحل**: إعادة تثبيت النظام
- 📞 **البديل**: التواصل مع الدعم الفني

---

## 📞 الدعم الفني

**للحصول على المساعدة:**
- 📧 **البريد الإلكتروني**: <EMAIL>
- 📱 **الهاتف**: +966-XX-XXX-XXXX
- 💬 **الدردشة**: موقع الشركة
- 🎫 **نظام التذاكر**: support.newsmart.com

**معلومات مطلوبة عند التواصل:**
- 🆔 **رقم الإصدار**: 0.29.0
- 💻 **نوع النسخة**: دائمة أم تجريبية
- 🖥️ **نظام التشغيل**: Windows 10/11
- 📝 **وصف المشكلة**: تفصيلي ودقيق

---

**📅 تاريخ الإنشاء**: 18 يونيو 2025
**👨‍💻 المطور**: Moneer al shawea
**🏢 الشركة**: نيوسمارت للحلول المحاسبية
**📄 الإصدار**: 1.0.0
