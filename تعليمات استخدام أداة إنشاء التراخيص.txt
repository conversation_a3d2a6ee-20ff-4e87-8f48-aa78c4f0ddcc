# تعليمات استخدام أداة إنشاء التراخيص

## 🎯 نظرة عامة

أداة إنشاء التراخيص هي تطبيق منفصل مخصص للمطورين لإنشاء مفاتيح ترخيص صحيحة للعملاء. هذه الأداة تضمن التوافق الكامل مع نظام newsmart المحاسبي.

---

## 📁 مكان الأداة

```
📁 license-generator-app/
├── 📄 package.json
├── 📄 main.js (الملف الرئيسي)
├── 📄 renderer.js (واجهة المستخدم)
├── 📄 index.html (الواجهة)
└── 📁 dist/ (الملفات المبنية)
    └── 📄 مولد التراخيص Setup 1.0.0.exe
```

---

## 🚀 تشغيل الأداة

### الطريقة الأولى: تشغيل الملف المبني
```bash
# انتقل إلى مجلد الأداة
cd license-generator-app

# شغل الملف المبني مباشرة
dist/مولد التراخيص Setup 1.0.0.exe
```

### الطريقة الثانية: البناء والتشغيل
```bash
# انتقل إلى مجلد الأداة
cd license-generator-app

# بناء الأداة
npm run build

# تشغيل الملف المبني
dist/مولد التراخيص Setup 1.0.0.exe
```

---

## 📋 خطوات إنشاء مفتاح ترخيص

### الخطوة 1: الحصول على الرقم التسلسلي من العميل

#### من جانب العميل:
1. العميل يشغل نظام newsmart لأول مرة
2. تظهر صفحة "تفعيل الترخيص" تلقائياً
3. يظهر الرقم التسلسلي في الصفحة (مثال: ERR-MC0L-GIWQ)
4. العميل ينسخ الرقم التسلسلي ويرسله للمطور

#### من جانب المطور:
1. استلام الرقم التسلسلي من العميل
2. التأكد من صحة تنسيق الرقم (XXX-XXXX-XXXX)
3. حفظ الرقم للاستخدام في الأداة

### الخطوة 2: فتح أداة إنشاء التراخيص

1. تشغيل ملف `مولد التراخيص Setup 1.0.0.exe`
2. انتظار تحميل الواجهة
3. التأكد من ظهور جميع الحقول بشكل صحيح

### الخطوة 3: إدخال البيانات

#### الحقول المطلوبة:
```
🔢 الرقم التسلسلي: [إدخال الرقم المستلم من العميل]
📝 نوع الترخيص: [اختيار من القائمة]
   ├── تجريبي (Trial) - 7 أيام
   └── دائم (Permanent) - بدون انتهاء
👤 اسم العميل: [اختياري]
📝 ملاحظات: [اختياري]
```

#### مثال عملي:
```
الرقم التسلسلي: ERR-MC0L-GIWQ
نوع الترخيص: دائم (Permanent)
اسم العميل: شركة الأعمال المتقدمة
ملاحظات: ترخيص كامل لجميع الوحدات
```

### الخطوة 4: إنشاء المفتاح

1. الضغط على زر "إنشاء مفتاح الترخيص"
2. انتظار معالجة البيانات (ثوانٍ قليلة)
3. ظهور المفتاح المُنشأ في الحقل المخصص

#### مثال للمفتاح المُنشأ:
```
نوع تجريبي: TRL-A1B2-C3D4-E5F6-G7H8
نوع دائم: PRM-6825-81C8-A2C8-5FAA
```

### الخطوة 5: نسخ وإرسال المفتاح

1. الضغط على زر "نسخ المفتاح"
2. التأكد من نسخ المفتاح بالكامل
3. إرسال المفتاح للعميل عبر:
   - البريد الإلكتروني
   - الواتساب
   - أي وسيلة تواصل آمنة

---

## 🔐 فهم تنسيق مفاتيح الترخيص

### تركيب المفتاح:
```
PREFIX-XXXX-XXXX-XXXX-XXXX

حيث:
├── PREFIX: نوع الترخيص
│   ├── TRL = تجريبي (Trial)
│   └── PRM = دائم (Permanent)
└── XXXX-XXXX-XXXX-XXXX: hash مشفر مرتبط بالجهاز
```

### أمثلة صحيحة:
```
✅ TRL-1234-ABCD-5678-EFGH (ترخيص تجريبي)
✅ PRM-9876-ZYXW-5432-QPON (ترخيص دائم)
```

### أمثلة خاطئة:
```
❌ TRL-1234-ABCD-5678 (أجزاء ناقصة)
❌ ABC-1234-ABCD-5678-EFGH (بادئة خاطئة)
❌ TRL 1234 ABCD 5678 EFGH (مسافات بدلاً من شرطات)
```

---

## 📊 سجل التراخيص

### عرض السجل:
1. في أداة إنشاء التراخيص، اضغط على "عرض السجل"
2. ستظهر قائمة بجميع المفاتيح المُنشأة مسبقاً
3. يمكن البحث والفلترة حسب التاريخ أو العميل

### معلومات السجل:
```
📅 تاريخ الإنشاء
🔑 مفتاح الترخيص
🔢 الرقم التسلسلي
📝 نوع الترخيص
👤 اسم العميل
📝 الملاحظات
```

### تصدير السجل:
1. اضغط على "تصدير السجل"
2. اختر مكان الحفظ
3. سيتم حفظ ملف JSON يحتوي على جميع البيانات

---

## ⚠️ تحذيرات مهمة

### للمطور:
```
🚨 لا تشارك أداة إنشاء التراخيص مع أي شخص آخر
🚨 احتفظ بنسخة احتياطية من سجل التراخيص
🚨 تأكد من تطابق إصدار الأداة مع إصدار النظام
🚨 لا تنشئ مفاتيح متعددة لنفس الرقم التسلسلي
```

### للعميل:
```
⚠️ لا تشارك مفتاح الترخيص مع الآخرين
⚠️ احتفظ بنسخة من مفتاح الترخيص في مكان آمن
⚠️ تواصل مع الدعم الفني عند مواجهة مشاكل
⚠️ لا تحاول تعديل أو تغيير مفتاح الترخيص
```

---

## 🔧 استكشاف الأخطاء

### مشاكل شائعة في الأداة:

#### 1. الأداة لا تبدأ:
```
الحلول:
✅ تشغيل كمدير (Run as Administrator)
✅ تعطيل برامج الحماية مؤقتاً
✅ إعادة تثبيت الأداة
✅ التأكد من تثبيت Node.js
```

#### 2. خطأ في إنشاء المفتاح:
```
الحلول:
✅ التأكد من صحة تنسيق الرقم التسلسلي
✅ إعادة تشغيل الأداة
✅ التحقق من مساحة القرص المتاحة
✅ التأكد من صلاحيات الكتابة
```

#### 3. المفتاح المُنشأ لا يعمل:
```
الحلول:
✅ التأكد من نسخ المفتاح بالكامل
✅ التأكد من عدم وجود مسافات إضافية
✅ التأكد من تطابق الرقم التسلسلي
✅ إنشاء مفتاح جديد
```

---

## 📞 الدعم الفني

### للمطورين:
```
📧 البريد الإلكتروني: <EMAIL>
📱 الهاتف: +966-XX-XXX-XXXX (داخلي 101)
💬 الدردشة: متوفرة في الأداة
🌐 الموقع: www.newsmart.com/dev-tools
```

### معلومات مطلوبة عند التواصل:
```
🔢 إصدار الأداة
🔢 إصدار النظام الأساسي
📝 وصف المشكلة
🖼️ لقطة شاشة (إن أمكن)
📋 رسالة الخطأ (إن وجدت)
```

---

## 📈 التحديثات المستقبلية

### ميزات قادمة:
```
🔄 تحديث تلقائي للأداة
📊 إحصائيات مفصلة للتراخيص
🔐 تشفير إضافي للمفاتيح
📱 إصدار ويب للأداة
🌐 ربط مع قاعدة بيانات مركزية
```

### جدولة التحديثات:
```
📅 تحديثات أمنية: شهرياً
📅 تحديثات الميزات: كل 3 أشهر
📅 تحديثات كبرى: سنوياً
```

---

## ✅ قائمة مراجعة سريعة

### قبل إنشاء مفتاح:
- [ ] تأكد من استلام الرقم التسلسلي الصحيح
- [ ] تأكد من تشغيل أداة إنشاء التراخيص
- [ ] تأكد من اختيار نوع الترخيص المناسب
- [ ] تأكد من إدخال معلومات العميل

### بعد إنشاء المفتاح:
- [ ] تأكد من نسخ المفتاح بالكامل
- [ ] تأكد من حفظ المفتاح في السجل
- [ ] تأكد من إرسال المفتاح للعميل
- [ ] تأكد من تأكيد استلام العميل

### عند مواجهة مشاكل:
- [ ] تحقق من رسائل الخطأ
- [ ] راجع تنسيق الرقم التسلسلي
- [ ] تأكد من تطابق إصدارات الأداة والنظام
- [ ] تواصل مع الدعم الفني عند الحاجة

---

**🎉 مبروك! أنت الآن جاهز لاستخدام أداة إنشاء التراخيص بكفاءة**

**📅 تاريخ الإنشاء**: 17 ديسمبر 2024
**🔢 إصدار الدليل**: 1.0.0
**👨‍💻 المطور**: Moneer al shawea
