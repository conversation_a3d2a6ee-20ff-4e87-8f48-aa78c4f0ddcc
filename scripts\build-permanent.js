// سكريبت بناء النسخة الدائمة المدمجة الترخيص
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 بدء بناء النسخة الدائمة المدمجة الترخيص...');

// مسار ملف الترخيص المدمج
const embeddedLicensePath = path.join(__dirname, '../src/utils/embeddedLicense.ts');

// قراءة الملف الحالي
let fileContent = fs.readFileSync(embeddedLicensePath, 'utf8');

// النسخة الاحتياطية من الملف الأصلي
const backupPath = embeddedLicensePath + '.backup';
fs.writeFileSync(backupPath, fileContent);

console.log('📝 تحديث إعدادات الترخيص للنسخة الدائمة...');

// تحديث الإعدادات للنسخة الدائمة
fileContent = fileContent.replace(
  'isEmbedded: false,',
  'isEmbedded: true,'
);

fileContent = fileContent.replace(
  'licenseType: \'permanent\',',
  'licenseType: \'permanent\','
);

fileContent = fileContent.replace(
  'maxUsers: 50,',
  'maxUsers: 50,'
);

fileContent = fileContent.replace(
  'allowedModules: [\'all\'],',
  'allowedModules: [\'all\'],'
);

fileContent = fileContent.replace(
  'clientName: \'عميل مخصص\',',
  'clientName: \'نسخة دائمة مرخصة\','
);

// كتابة الملف المحدث
fs.writeFileSync(embeddedLicensePath, fileContent);

console.log('✅ تم تحديث إعدادات الترخيص');

try {
  console.log('🔨 بناء النظام...');
  
  // بناء النظام
  execSync('npm run build', { 
    stdio: 'inherit',
    cwd: path.join(__dirname, '..')
  });
  
  console.log('✅ تم بناء النظام بنجاح');
  
  // نسخ الملفات إلى مجلد التوزيع
  const sourceSetup = path.join(__dirname, '../dist_electron/bundled/newsmart Setup 0.29.0.exe');
  const sourcePortable = path.join(__dirname, '../dist_electron/bundled/newsmart 0.29.0.exe');
  const distDir = path.join(__dirname, '../distribution');
  
  // إنشاء مجلد التوزيع إذا لم يكن موجود
  if (!fs.existsSync(distDir)) {
    fs.mkdirSync(distDir, { recursive: true });
  }
  
  // نسخ النسخة الدائمة
  if (fs.existsSync(sourceSetup)) {
    const targetSetup = path.join(distDir, 'newsmart-permanent-embedded.exe');
    fs.copyFileSync(sourceSetup, targetSetup);
    console.log('✅ تم نسخ نسخة التثبيت الدائمة:', targetSetup);
  }
  
  if (fs.existsSync(sourcePortable)) {
    const targetPortable = path.join(distDir, 'newsmart-permanent-portable.exe');
    fs.copyFileSync(sourcePortable, targetPortable);
    console.log('✅ تم نسخ النسخة المحمولة الدائمة:', targetPortable);
  }
  
} catch (error) {
  console.error('❌ خطأ في البناء:', error.message);
} finally {
  // استعادة الملف الأصلي
  console.log('🔄 استعادة الملف الأصلي...');
  fs.writeFileSync(embeddedLicensePath, fs.readFileSync(backupPath, 'utf8'));
  fs.unlinkSync(backupPath);
  console.log('✅ تم استعادة الملف الأصلي');
}

console.log('🎉 انتهى بناء النسخة الدائمة!');
