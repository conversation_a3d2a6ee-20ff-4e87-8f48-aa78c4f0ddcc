# تعليمات إضافية - دليل المطور الشامل للنظام المحاسبي

## 📋 فهرس المحتويات
1. هيكل المشروع
2. إضافة مديول جديد
3. إضافة صفحة جديدة
4. إظهار المديولات في الشريط الجانبي
5. إنشاء جداول قاعدة البيانات
6. الأوامر المستخدمة
7. حل الأخطاء الشائعة
8. إظهار المديولات المخفية
9. أمثلة عملية

---

## 🏗️ 1. هيكل المشروع

### الملفات الأساسية:
```
books/
├── src/
│   ├── components/          # المكونات المشتركة
│   ├── pages/              # الصفحات الرئيسية
│   ├── utils/              # الأدوات المساعدة
│   │   ├── sidebarConfig.ts # إعدادات الشريط الجانبي
│   │   └── theme.ts        # إعدادات الثيم
│   ├── styles/             # ملفات التصميم
│   ├── router.ts           # إعدادات التوجيه
│   └── App.vue            # التطبيق الرئيسي
├── models/                 # نماذج قاعدة البيانات
├── schemas/               # مخططات قاعدة البيانات
├── package.json           # إعدادات المشروع
└── tailwind.config.js     # إعدادات Tailwind CSS
```

### الملفات المهمة:
- `src/utils/sidebarConfig.ts` - تحكم في القائمة الجانبية
- `src/router.ts` - تحكم في المسارات
- `models/` - نماذج البيانات
- `schemas/` - مخططات قاعدة البيانات

---

## 🆕 2. إضافة مديول جديد

### الخطوة 1: إنشاء مجلد المديول
```bash
mkdir src/pages/NewModule
```

### الخطوة 2: إنشاء الصفحة الرئيسية
```vue
<!-- src/pages/NewModule/NewModule.vue -->
<template>
  <div class="new-module-page">
    <PageHeader :title="t`المديول الجديد`" />
    
    <div class="p-6">
      <div class="content-card p-6">
        <h2 class="theme-text-primary text-xl font-semibold mb-4">
          {{ t`مرحباً بك في المديول الجديد` }}
        </h2>
        <p class="theme-text-secondary">
          {{ t`هذا مديول جديد تم إضافته للنظام` }}
        </p>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import PageHeader from 'src/components/PageHeader.vue';

export default defineComponent({
  name: 'NewModule',
  components: {
    PageHeader,
  },
});
</script>
```

### الخطوة 3: إضافة المسار في router.ts
```typescript
// في src/router.ts
import NewModule from 'src/pages/NewModule/NewModule.vue';

// إضافة في routes array:
{
  path: '/new-module',
  name: 'New Module',
  component: NewModule,
  meta: { requiresAuth: true }
},
```

### الخطوة 4: إضافة في الشريط الجانبي
```typescript
// في src/utils/sidebarConfig.ts
{
  label: t`المديول الجديد`,
  name: 'new-module',
  route: '/new-module',
  icon: 'grid',
  hidden: () => false, // دائماً مرئي
},
```

---

## 📄 3. إضافة صفحة جديدة

### صفحة بسيطة:
```vue
<!-- src/pages/NewPage.vue -->
<template>
  <div class="new-page">
    <PageHeader :title="t`صفحة جديدة`" />
    
    <div class="p-6 space-y-6">
      <!-- محتوى الصفحة -->
      <div class="dashboard-card">
        <h3 class="theme-text-primary text-lg font-semibold mb-3">
          {{ t`عنوان القسم` }}
        </h3>
        <p class="theme-text-secondary">
          {{ t`محتوى الصفحة هنا` }}
        </p>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import PageHeader from 'src/components/PageHeader.vue';

export default defineComponent({
  name: 'NewPage',
  components: {
    PageHeader,
  },
  data() {
    return {
      // بيانات الصفحة
    };
  },
  methods: {
    // دوال الصفحة
  },
});
</script>
```

### إضافة المسار:
```typescript
// في src/router.ts
import NewPage from 'src/pages/NewPage.vue';

{
  path: '/new-page',
  name: 'New Page',
  component: NewPage,
  meta: { requiresAuth: true }
},
```

---

## 🔧 4. إظهار المديولات في الشريط الجانبي

### إضافة مديول رئيسي:
```typescript
// في src/utils/sidebarConfig.ts
{
  label: t`اسم المديول`,
  name: 'module-name',
  route: '/module-route',
  icon: 'icon-name', // من Feather Icons
  hidden: () => {
    // شروط الإظهار/الإخفاء
    const currentUser = localStorage.getItem('currentUser');
    return !currentUser; // مخفي إذا لم يكن هناك مستخدم
  },
},
```

### إضافة مديول بصفحات فرعية:
```typescript
{
  label: t`المديول الرئيسي`,
  name: 'main-module',
  route: '/main-module',
  icon: 'folder',
  items: [
    {
      label: t`الصفحة الفرعية 1`,
      name: 'sub-page-1',
      route: '/main-module/sub-page-1',
    },
    {
      label: t`الصفحة الفرعية 2`,
      name: 'sub-page-2',
      route: '/main-module/sub-page-2',
    },
  ],
  hidden: () => false,
},
```

### شروط الإظهار المختلفة:
```typescript
// إظهار للمدير فقط
hidden: () => {
  const userRole = localStorage.getItem('userRole');
  return userRole !== 'Admin';
},

// إظهار لمستخدمين محددين
hidden: () => {
  const currentUser = localStorage.getItem('currentUser');
  const allowedUsers = ['admin', 'manager'];
  return !allowedUsers.includes(currentUser);
},

// إظهار حسب الصلاحيات
hidden: () => {
  const permissions = JSON.parse(localStorage.getItem('userPermissions') || '[]');
  return !permissions.includes('module_access');
},
```

---

## 🗄️ 5. إنشاء جداول قاعدة البيانات

### الخطوة 1: إنشاء Schema
```javascript
// في schemas/app/NewTable.json
{
  "name": "NewTable",
  "label": "الجدول الجديد",
  "naming": "autoincrement",
  "fields": [
    {
      "fieldname": "name",
      "label": "الاسم",
      "fieldtype": "Data",
      "required": true
    },
    {
      "fieldname": "description",
      "label": "الوصف",
      "fieldtype": "Text"
    },
    {
      "fieldname": "amount",
      "label": "المبلغ",
      "fieldtype": "Currency"
    },
    {
      "fieldname": "date",
      "label": "التاريخ",
      "fieldtype": "Date"
    },
    {
      "fieldname": "isActive",
      "label": "نشط",
      "fieldtype": "Check",
      "default": true
    }
  ]
}
```

### الخطوة 2: إنشاء Model
```typescript
// في models/app/NewTable.ts
import { Doc } from 'fyo/model/doc';
import { FiltersMap, FormulaMap } from 'fyo/model/types';

export class NewTable extends Doc {
  name?: string;
  description?: string;
  amount?: number;
  date?: Date;
  isActive?: boolean;

  // دوال مخصصة
  async beforeInsert() {
    // منطق قبل الإدراج
  }

  async afterInsert() {
    // منطق بعد الإدراج
  }

  // فلاتر
  static filters: FiltersMap = {
    // فلاتر مخصصة
  };

  // صيغ محسوبة
  static formulas: FormulaMap = {
    // صيغ مخصصة
  };
}
```

### الخطوة 3: تسجيل النموذج
```typescript
// في models/index.ts
import { NewTable } from './app/NewTable';

export const models = {
  // النماذج الموجودة...
  NewTable,
};
```

### الخطوة 4: إنشاء صفحة إدارة البيانات
```vue
<!-- src/pages/NewTable/NewTableList.vue -->
<template>
  <div class="new-table-list">
    <PageHeader :title="t`إدارة الجدول الجديد`">
      <template #actions>
        <Button @click="createNew">{{ t`إضافة جديد` }}</Button>
      </template>
    </PageHeader>
    
    <div class="p-6">
      <!-- جدول البيانات -->
      <div class="table-container">
        <table class="w-full">
          <thead class="table-header">
            <tr>
              <th class="px-4 py-3 text-right">{{ t`الاسم` }}</th>
              <th class="px-4 py-3 text-right">{{ t`الوصف` }}</th>
              <th class="px-4 py-3 text-right">{{ t`المبلغ` }}</th>
              <th class="px-4 py-3 text-right">{{ t`التاريخ` }}</th>
              <th class="px-4 py-3 text-right">{{ t`الإجراءات` }}</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="item in items" :key="item.name" class="table-row border-b">
              <td class="px-4 py-3">{{ item.name }}</td>
              <td class="px-4 py-3">{{ item.description }}</td>
              <td class="px-4 py-3">{{ item.amount }}</td>
              <td class="px-4 py-3">{{ item.date }}</td>
              <td class="px-4 py-3">
                <button @click="editItem(item)" class="btn-primary px-3 py-1 text-sm rounded">
                  {{ t`تعديل` }}
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { fyo } from 'src/initFyo';

export default defineComponent({
  name: 'NewTableList',
  data() {
    return {
      items: [],
    };
  },
  async mounted() {
    await this.loadData();
  },
  methods: {
    async loadData() {
      try {
        this.items = await fyo.db.getAll('NewTable');
      } catch (error) {
        console.error('Error loading data:', error);
      }
    },
    
    async createNew() {
      const doc = fyo.doc.getNewDoc('NewTable');
      // فتح نموذج التحرير
    },
    
    async editItem(item) {
      const doc = await fyo.doc.getDoc('NewTable', item.name);
      // فتح نموذج التحرير
    },
  },
});
</script>
```

---

## ⚡ 6. الأوامر المستخدمة

### أوامر التطوير:
```bash
# تشغيل النظام في وضع التطوير
npm run dev

# بناء النظام للإنتاج
npm run build

# تشغيل الاختبارات
npm test

# فحص الكود
npm run lint

# إصلاح مشاكل الكود
npm run lint:fix
```

### أوامر قاعدة البيانات:
```bash
# إنشاء قاعدة بيانات جديدة
npm run db:create

# تحديث مخططات قاعدة البيانات
npm run db:migrate

# إعادة تعيين قاعدة البيانات
npm run db:reset
```

### أوامر Git:
```bash
# إضافة التغييرات
git add .

# حفظ التغييرات
git commit -m "وصف التغيير"

# رفع التغييرات
git push origin main

# سحب التحديثات
git pull origin main
```

---

## 🔧 7. حل الأخطاء الشائعة

### خطأ: Module not found
```bash
# المشكلة: لا يمكن العثور على المديول
# الحل:
1. تأكد من مسار الملف
2. تأكد من تصدير المكون بشكل صحيح
3. أعد تشغيل الخادم: npm run dev
```

### خطأ: Cannot read property of undefined
```javascript
// المشكلة: محاولة الوصول لخاصية غير موجودة
// الحل:
// بدلاً من:
const value = data.property;

// استخدم:
const value = data?.property || 'قيمة افتراضية';
```

### خطأ: Router not found
```typescript
// المشكلة: المسار غير مسجل
// الحل: تأكد من إضافة المسار في router.ts
{
  path: '/your-path',
  name: 'Your Page',
  component: YourComponent,
}
```

### خطأ: Database schema not found
```bash
# المشكلة: مخطط قاعدة البيانات غير موجود
# الحل:
1. تأكد من وجود ملف Schema في schemas/app/
2. أعد تشغيل النظام
3. تحقق من صحة JSON في ملف Schema
```

### خطأ: CSS classes not working
```bash
# المشكلة: كلاسات CSS لا تعمل
# الحل:
1. تأكد من استيراد ملفات CSS
2. تحقق من Tailwind config
3. امسح cache: rm -rf node_modules/.cache
```

---

## 👁️ 8. إظهار المديولات المخفية

### إظهار معلومات الترخيص:
```typescript
// في src/utils/sidebarConfig.ts
{
  label: t`معلومات الترخيص`,
  name: 'license-info',
  route: '/license-info',
  hidden: () => {
    // غير الشرط من true إلى false
    return false; // سيظهر المديول
  },
},
```

### إظهار مولد التراخيص:
```typescript
{
  label: t`مولد التراخيص`,
  name: 'license-generator',
  route: '/license-generator',
  hidden: () => {
    // إظهار للمدير فقط
    const currentUser = localStorage.getItem('currentUser');
    return currentUser !== 'admin';
  },
},
```

### إظهار مديول حسب الصلاحيات:
```typescript
{
  label: t`المديول المخفي`,
  name: 'hidden-module',
  route: '/hidden-module',
  hidden: () => {
    // شروط مخصصة للإظهار
    const userRole = localStorage.getItem('userRole');
    const currentUser = localStorage.getItem('currentUser');
    
    // إظهار للمدير أو مستخدمين محددين
    return !(userRole === 'Admin' || currentUser === 'special_user');
  },
},
```

---

## 📝 9. أمثلة عملية

### مثال 1: إضافة مديول إدارة العملاء
```bash
# 1. إنشاء المجلد
mkdir src/pages/Customers

# 2. إنشاء الملفات
touch src/pages/Customers/CustomerList.vue
touch src/pages/Customers/CustomerForm.vue
```

```vue
<!-- CustomerList.vue -->
<template>
  <div class="customers-page">
    <PageHeader :title="t`إدارة العملاء`">
      <template #actions>
        <Button @click="addCustomer">{{ t`إضافة عميل` }}</Button>
      </template>
    </PageHeader>
    <!-- باقي المحتوى -->
  </div>
</template>
```

```typescript
// إضافة في router.ts
{
  path: '/customers',
  name: 'Customers',
  component: () => import('src/pages/Customers/CustomerList.vue'),
  meta: { requiresAuth: true }
},
```

```typescript
// إضافة في sidebarConfig.ts
{
  label: t`العملاء`,
  name: 'customers',
  route: '/customers',
  icon: 'users',
  hidden: () => false,
},
```

### مثال 2: إنشاء جدول المنتجات
```json
// schemas/app/Product.json
{
  "name": "Product",
  "label": "المنتج",
  "fields": [
    {
      "fieldname": "name",
      "label": "اسم المنتج",
      "fieldtype": "Data",
      "required": true
    },
    {
      "fieldname": "price",
      "label": "السعر",
      "fieldtype": "Currency",
      "required": true
    },
    {
      "fieldname": "category",
      "label": "الفئة",
      "fieldtype": "Link",
      "target": "Category"
    }
  ]
}
```

---

## 🚀 10. نصائح للتطوير

### أفضل الممارسات:
1. **استخدم TypeScript** لتجنب الأخطاء
2. **اتبع تسمية موحدة** للملفات والمتغيرات
3. **اكتب تعليقات** للكود المعقد
4. **اختبر التغييرات** قبل الحفظ
5. **استخدم Git** لحفظ التغييرات

### تحسين الأداء:
1. **استخدم lazy loading** للصفحات الكبيرة
2. **قلل من استيراد المكتبات** غير الضرورية
3. **استخدم computed properties** بدلاً من methods
4. **احذف console.log** في الإنتاج

### الأمان:
1. **تحقق من صلاحيات المستخدم** في كل صفحة
2. **لا تحفظ كلمات المرور** في localStorage
3. **استخدم HTTPS** في الإنتاج
4. **تحقق من البيانات** قبل الحفظ

---

## 📞 الدعم والمساعدة

### عند مواجهة مشاكل:
1. **تحقق من وحدة التحكم** للأخطاء
2. **راجع ملفات السجل** للتفاصيل
3. **ابحث في التوثيق** عن حلول
4. **اسأل المجتمع** في المنتديات

### موارد مفيدة:
- **Vue.js Documentation**: https://vuejs.org/
- **TypeScript Handbook**: https://www.typescriptlang.org/
- **Tailwind CSS**: https://tailwindcss.com/
- **Frappe Framework**: https://frappeframework.com/

---

---

## 🔍 11. أمثلة متقدمة

### إنشاء مديول تقارير مخصص:
```vue
<!-- src/pages/Reports/CustomReports.vue -->
<template>
  <div class="custom-reports">
    <PageHeader :title="t`التقارير المخصصة`" />

    <div class="p-6 space-y-6">
      <!-- فلاتر التقرير -->
      <div class="content-card p-6">
        <h3 class="theme-text-primary text-lg font-semibold mb-4">{{ t`فلاتر التقرير` }}</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label class="theme-text-primary font-medium">{{ t`من تاريخ` }}</label>
            <input v-model="filters.fromDate" type="date" class="form-input w-full mt-1">
          </div>
          <div>
            <label class="theme-text-primary font-medium">{{ t`إلى تاريخ` }}</label>
            <input v-model="filters.toDate" type="date" class="form-input w-full mt-1">
          </div>
          <div>
            <label class="theme-text-primary font-medium">{{ t`نوع التقرير` }}</label>
            <select v-model="filters.reportType" class="form-input w-full mt-1">
              <option value="sales">{{ t`المبيعات` }}</option>
              <option value="purchases">{{ t`المشتريات` }}</option>
              <option value="inventory">{{ t`المخزون` }}</option>
            </select>
          </div>
        </div>
        <div class="mt-4">
          <button @click="generateReport" class="btn-primary px-6 py-2 rounded-lg">
            {{ t`إنشاء التقرير` }}
          </button>
        </div>
      </div>

      <!-- نتائج التقرير -->
      <div v-if="reportData.length > 0" class="content-card p-6">
        <h3 class="theme-text-primary text-lg font-semibold mb-4">{{ t`نتائج التقرير` }}</h3>
        <div class="table-container">
          <table class="w-full">
            <thead class="table-header">
              <tr>
                <th v-for="column in reportColumns" :key="column.key" class="px-4 py-3 text-right">
                  {{ column.label }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="row in reportData" :key="row.id" class="table-row border-b">
                <td v-for="column in reportColumns" :key="column.key" class="px-4 py-3">
                  {{ formatValue(row[column.key], column.type) }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { fyo } from 'src/initFyo';

export default defineComponent({
  name: 'CustomReports',
  data() {
    return {
      filters: {
        fromDate: '',
        toDate: '',
        reportType: 'sales'
      },
      reportData: [],
      reportColumns: []
    };
  },
  methods: {
    async generateReport() {
      try {
        // منطق إنشاء التقرير حسب النوع
        switch (this.filters.reportType) {
          case 'sales':
            await this.generateSalesReport();
            break;
          case 'purchases':
            await this.generatePurchasesReport();
            break;
          case 'inventory':
            await this.generateInventoryReport();
            break;
        }
      } catch (error) {
        console.error('Error generating report:', error);
      }
    },

    async generateSalesReport() {
      this.reportColumns = [
        { key: 'date', label: 'التاريخ', type: 'date' },
        { key: 'customer', label: 'العميل', type: 'text' },
        { key: 'amount', label: 'المبلغ', type: 'currency' }
      ];

      // استعلام قاعدة البيانات
      this.reportData = await fyo.db.getAll('SalesInvoice', {
        filters: {
          date: ['between', [this.filters.fromDate, this.filters.toDate]]
        }
      });
    },

    formatValue(value: any, type: string) {
      switch (type) {
        case 'currency':
          return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR'
          }).format(value);
        case 'date':
          return new Date(value).toLocaleDateString('ar-SA');
        default:
          return value;
      }
    }
  }
});
</script>
```

### إنشاء مكون قابل لإعادة الاستخدام:
```vue
<!-- src/components/DataTable.vue -->
<template>
  <div class="data-table">
    <div class="table-container">
      <table class="w-full">
        <thead class="table-header">
          <tr>
            <th v-for="column in columns" :key="column.key"
                class="px-4 py-3 text-right cursor-pointer"
                @click="sortBy(column.key)">
              {{ column.label }}
              <span v-if="sortColumn === column.key">
                {{ sortDirection === 'asc' ? '↑' : '↓' }}
              </span>
            </th>
            <th v-if="actions.length > 0" class="px-4 py-3 text-right">{{ t`الإجراءات` }}</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="row in sortedData" :key="row.id || row.name" class="table-row border-b">
            <td v-for="column in columns" :key="column.key" class="px-4 py-3">
              <slot :name="`cell-${column.key}`" :row="row" :value="row[column.key]">
                {{ formatCellValue(row[column.key], column.type) }}
              </slot>
            </td>
            <td v-if="actions.length > 0" class="px-4 py-3">
              <div class="flex space-x-2 space-x-reverse">
                <button v-for="action in actions" :key="action.name"
                        @click="$emit('action', action.name, row)"
                        :class="action.class || 'btn-primary px-3 py-1 text-sm rounded'">
                  {{ action.label }}
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div v-if="pagination" class="flex justify-between items-center mt-4">
      <div class="theme-text-secondary">
        {{ t`عرض` }} {{ startIndex }} - {{ endIndex }} {{ t`من` }} {{ totalItems }}
      </div>
      <div class="flex space-x-2 space-x-reverse">
        <button @click="previousPage" :disabled="currentPage === 1"
                class="px-3 py-1 border rounded disabled:opacity-50">
          {{ t`السابق` }}
        </button>
        <button @click="nextPage" :disabled="currentPage === totalPages"
                class="px-3 py-1 border rounded disabled:opacity-50">
          {{ t`التالي` }}
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';

export default defineComponent({
  name: 'DataTable',
  props: {
    data: { type: Array, required: true },
    columns: { type: Array, required: true },
    actions: { type: Array, default: () => [] },
    pagination: { type: Boolean, default: false },
    itemsPerPage: { type: Number, default: 10 }
  },
  emits: ['action'],
  data() {
    return {
      sortColumn: '',
      sortDirection: 'asc',
      currentPage: 1
    };
  },
  computed: {
    sortedData() {
      let data = [...this.data];

      if (this.sortColumn) {
        data.sort((a, b) => {
          const aVal = a[this.sortColumn];
          const bVal = b[this.sortColumn];

          if (this.sortDirection === 'asc') {
            return aVal > bVal ? 1 : -1;
          } else {
            return aVal < bVal ? 1 : -1;
          }
        });
      }

      if (this.pagination) {
        const start = (this.currentPage - 1) * this.itemsPerPage;
        const end = start + this.itemsPerPage;
        return data.slice(start, end);
      }

      return data;
    },

    totalItems() {
      return this.data.length;
    },

    totalPages() {
      return Math.ceil(this.totalItems / this.itemsPerPage);
    },

    startIndex() {
      return (this.currentPage - 1) * this.itemsPerPage + 1;
    },

    endIndex() {
      return Math.min(this.currentPage * this.itemsPerPage, this.totalItems);
    }
  },
  methods: {
    sortBy(column: string) {
      if (this.sortColumn === column) {
        this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
      } else {
        this.sortColumn = column;
        this.sortDirection = 'asc';
      }
    },

    formatCellValue(value: any, type: string) {
      switch (type) {
        case 'currency':
          return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR'
          }).format(value);
        case 'date':
          return new Date(value).toLocaleDateString('ar-SA');
        case 'boolean':
          return value ? 'نعم' : 'لا';
        default:
          return value;
      }
    },

    previousPage() {
      if (this.currentPage > 1) {
        this.currentPage--;
      }
    },

    nextPage() {
      if (this.currentPage < this.totalPages) {
        this.currentPage++;
      }
    }
  }
});
</script>
```

---

## 🛠️ 12. أدوات التطوير المتقدمة

### إعداد ESLint للجودة:
```json
// .eslintrc.js
module.exports = {
  extends: [
    '@vue/typescript/recommended',
    'prettier'
  ],
  rules: {
    'no-console': 'warn',
    'no-debugger': 'error',
    '@typescript-eslint/no-unused-vars': 'error',
    'vue/no-unused-components': 'error'
  }
};
```

### إعداد Prettier للتنسيق:
```json
// .prettierrc
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2
}
```

### إعداد Husky للـ Git Hooks:
```json
// package.json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged",
      "pre-push": "npm test"
    }
  },
  "lint-staged": {
    "*.{js,ts,vue}": ["eslint --fix", "prettier --write"]
  }
}
```

---

## 📊 13. مراقبة الأداء والتحليل

### إضافة مراقبة الأداء:
```typescript
// src/utils/performance.ts
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, number> = new Map();

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  startTimer(name: string): void {
    this.metrics.set(name, performance.now());
  }

  endTimer(name: string): number {
    const startTime = this.metrics.get(name);
    if (!startTime) return 0;

    const duration = performance.now() - startTime;
    console.log(`${name} took ${duration.toFixed(2)}ms`);
    return duration;
  }

  measureFunction<T>(name: string, fn: () => T): T {
    this.startTimer(name);
    const result = fn();
    this.endTimer(name);
    return result;
  }
}

// الاستخدام:
const monitor = PerformanceMonitor.getInstance();
monitor.startTimer('data-load');
// ... تحميل البيانات
monitor.endTimer('data-load');
```

### إضافة تسجيل الأخطاء:
```typescript
// src/utils/errorLogger.ts
export class ErrorLogger {
  static logError(error: Error, context?: string): void {
    const errorInfo = {
      message: error.message,
      stack: error.stack,
      context: context || 'Unknown',
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    // إرسال للخادم أو حفظ محلياً
    console.error('Error logged:', errorInfo);

    // يمكن إرسال للخادم
    // this.sendToServer(errorInfo);
  }

  static async sendToServer(errorInfo: any): Promise<void> {
    try {
      await fetch('/api/errors', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(errorInfo)
      });
    } catch (e) {
      console.error('Failed to send error to server:', e);
    }
  }
}
```

---

## 🔐 14. الأمان والصلاحيات المتقدمة

### إنشاء نظام صلاحيات متقدم:
```typescript
// src/utils/permissions.ts
export interface Permission {
  module: string;
  action: 'read' | 'write' | 'delete' | 'admin';
  resource?: string;
}

export class PermissionManager {
  private static userPermissions: Permission[] = [];

  static setUserPermissions(permissions: Permission[]): void {
    this.userPermissions = permissions;
  }

  static hasPermission(module: string, action: string, resource?: string): boolean {
    return this.userPermissions.some(permission =>
      permission.module === module &&
      permission.action === action &&
      (!resource || permission.resource === resource)
    );
  }

  static canAccess(route: string): boolean {
    const routePermissions = {
      '/sales': { module: 'sales', action: 'read' },
      '/purchases': { module: 'purchases', action: 'read' },
      '/reports': { module: 'reports', action: 'read' },
      '/users': { module: 'users', action: 'admin' }
    };

    const required = routePermissions[route];
    if (!required) return true;

    return this.hasPermission(required.module, required.action);
  }
}

// استخدام في router guard:
router.beforeEach((to, from, next) => {
  if (PermissionManager.canAccess(to.path)) {
    next();
  } else {
    next('/unauthorized');
  }
});
```

---

## 📱 15. التوافق مع الأجهزة المحمولة

### إضافة تصميم متجاوب:
```vue
<!-- src/components/ResponsiveLayout.vue -->
<template>
  <div class="responsive-layout">
    <!-- Desktop Sidebar -->
    <div v-if="!isMobile" class="sidebar-container">
      <Sidebar />
    </div>

    <!-- Mobile Menu -->
    <div v-if="isMobile" class="mobile-menu">
      <button @click="toggleMobileMenu" class="menu-toggle">
        <feather-icon name="menu" />
      </button>

      <div v-if="showMobileMenu" class="mobile-sidebar">
        <Sidebar @item-click="closeMobileMenu" />
      </div>
    </div>

    <!-- Main Content -->
    <div class="main-content" :class="{ 'mobile': isMobile }">
      <slot />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'ResponsiveLayout',
  data() {
    return {
      isMobile: false,
      showMobileMenu: false
    };
  },
  mounted() {
    this.checkScreenSize();
    window.addEventListener('resize', this.checkScreenSize);
  },
  unmounted() {
    window.removeEventListener('resize', this.checkScreenSize);
  },
  methods: {
    checkScreenSize() {
      this.isMobile = window.innerWidth < 768;
    },

    toggleMobileMenu() {
      this.showMobileMenu = !this.showMobileMenu;
    },

    closeMobileMenu() {
      this.showMobileMenu = false;
    }
  }
});
</script>

<style scoped>
.mobile-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: var(--content-bg);
  padding: 1rem;
  border-bottom: 1px solid var(--content-border);
}

.mobile-sidebar {
  position: fixed;
  top: 60px;
  left: 0;
  bottom: 0;
  width: 280px;
  background: var(--sidebar-bg);
  z-index: 999;
  transform: translateX(0);
  transition: transform 0.3s ease;
}

.main-content.mobile {
  padding-top: 60px;
}

@media (max-width: 768px) {
  .sidebar-container {
    display: none;
  }
}
</style>
```

---

---

## 📦 16. تحويل التطبيق إلى ملف exe وتثبيته للعميل

### متطلبات التحويل:
```bash
# تأكد من تثبيت electron-builder
npm install electron-builder --save-dev

# تأكد من وجود Node.js 16+ و npm
node --version
npm --version
```

### الخطوة 1: إعداد ملف package.json
```json
// في package.json - قسم build
{
  "build": {
    "appId": "com.newsmart.app",
    "productName": "newsmart",
    "copyright": "Copyright © 2025 Moneer al shawea.a",
    "directories": {
      "output": "dist"
    },
    "files": [
      "dist",
      "node_modules",
      "build",
      "package.json"
    ],
    "extraResources": [
      "templates",
      "translations",
      "dbs"
    ],
    "win": {
      "icon": "build/icons/newsmart.ico",
      "target": [
        {
          "target": "nsis",
          "arch": ["x64", "ia32"]
        },
        {
          "target": "portable",
          "arch": ["x64", "ia32"]
        }
      ],
      "publisherName": "Moneer al shawea",
      "requestedExecutionLevel": "asInvoker"
    },
    "nsis": {
      "oneClick": false,
      "perMachine": false,
      "allowToChangeInstallationDirectory": true,
      "installerIcon": "build/icons/newsmart.ico",
      "uninstallerIcon": "build/icons/uninstallerIcon.ico",
      "createDesktopShortcut": true,
      "createStartMenuShortcut": true,
      "shortcutName": "newsmart",
      "include": "build/installer.nsh"
    }
  }
}
```

### الخطوة 2: إعداد الأيقونات
```bash
# إنشاء مجلد الأيقونات
mkdir -p build/icons

# نسخ الأيقونات المطلوبة:
# - newsmart.ico (256x256 للويندوز)
# - newsmart.icns (للماك)
# - newsmart.png (512x512 للينكس)
# - installerIcon.ico (أيقونة المثبت)
# - uninstallerIcon.ico (أيقونة إلغاء التثبيت)
```

### الخطوة 3: إعداد ملف المثبت المخصص
```nsis
# build/installer.nsh
!macro customInstall
  # إنشاء مجلد البيانات
  CreateDirectory "$APPDATA\newsmart"
  CreateDirectory "$APPDATA\newsmart\databases"
  CreateDirectory "$APPDATA\newsmart\logs"

  # نسخ الملفات الافتراضية
  SetOutPath "$APPDATA\newsmart"
  File /r "${BUILD_RESOURCES_DIR}\templates"
  File /r "${BUILD_RESOURCES_DIR}\translations"

  # إنشاء اختصارات إضافية
  CreateShortCut "$DESKTOP\newsmart.lnk" "$INSTDIR\newsmart.exe"
  CreateShortCut "$SMPROGRAMS\newsmart\newsmart.lnk" "$INSTDIR\newsmart.exe"
!macroend

!macro customUnInstall
  # حذف بيانات المستخدم (اختياري)
  RMDir /r "$APPDATA\newsmart"
  Delete "$DESKTOP\newsmart.lnk"
!macroend
```

### الخطوة 4: أوامر البناء والتحويل
```bash
# 1. تنظيف المشروع
npm run clean
# أو
rm -rf dist dist_electron node_modules/.cache

# 2. تثبيت التبعيات
npm install

# 3. بناء التطبيق للإنتاج
npm run build

# 4. تحويل إلى exe (ويندوز)
npm run electron:build

# أو استخدام electron-builder مباشرة:
npx electron-builder --win --x64

# للحصول على نسخة محمولة (portable):
npx electron-builder --win --x64 --config.win.target=portable

# للحصول على مثبت NSIS:
npx electron-builder --win --x64 --config.win.target=nsis
```

### الخطوة 5: إعداد البناء التلقائي
```json
// إضافة scripts في package.json
{
  "scripts": {
    "electron:build": "vue-tsc --noEmit && vite build && electron-builder",
    "electron:build:win": "npm run build && electron-builder --win",
    "electron:build:mac": "npm run build && electron-builder --mac",
    "electron:build:linux": "npm run build && electron-builder --linux",
    "electron:build:all": "npm run build && electron-builder --win --mac --linux",
    "build:portable": "npm run build && electron-builder --win --config.win.target=portable",
    "build:installer": "npm run build && electron-builder --win --config.win.target=nsis",
    "clean": "rimraf dist dist_electron"
  }
}
```

### الخطوة 6: إعداد التوقيع الرقمي (اختياري)
```json
// في package.json - قسم build.win
{
  "win": {
    "certificateFile": "path/to/certificate.p12",
    "certificatePassword": "password",
    "signingHashAlgorithms": ["sha256"],
    "signDlls": true
  }
}
```

### الخطوة 7: إعداد التحديث التلقائي
```typescript
// في main.ts
import { autoUpdater } from 'electron-updater';

// إعداد التحديث التلقائي
autoUpdater.checkForUpdatesAndNotify();

// معالجة أحداث التحديث
autoUpdater.on('checking-for-update', () => {
  console.log('Checking for update...');
});

autoUpdater.on('update-available', (info) => {
  console.log('Update available.');
});

autoUpdater.on('update-not-available', (info) => {
  console.log('Update not available.');
});
```

### الخطوة 8: اختبار التطبيق قبل التوزيع
```bash
# تشغيل التطبيق في وضع التطوير
npm run electron:serve

# اختبار البناء محلياً
npm run electron:build

# اختبار الملف المحمول
./dist/newsmart-portable.exe

# اختبار المثبت
./dist/newsmart-setup.exe
```

---

## 🚀 17. توزيع التطبيق للعملاء

### إعداد حزمة التثبيت الكاملة:
```bash
# إنشاء مجلد التوزيع
mkdir distribution
cd distribution

# نسخ ملفات التثبيت
cp ../dist/newsmart-setup.exe ./
cp ../dist/newsmart-portable.exe ./

# إنشاء ملف README للعميل
touch README-العميل.txt
```

### محتوى ملف README للعميل:
```text
# دليل تثبيت وتشغيل نظام newsmart المحاسبي

## متطلبات النظام:
- نظام التشغيل: Windows 10/11 (64-bit)
- الذاكرة: 4 GB RAM كحد أدنى
- مساحة القرص: 500 MB مساحة فارغة
- دقة الشاشة: 1024x768 كحد أدنى

## طرق التثبيت:

### الطريقة الأولى: التثبيت الكامل (مستحسن)
1. شغل ملف "newsmart-setup.exe"
2. اتبع تعليمات المثبت
3. اختر مجلد التثبيت (افتراضي: C:\Program Files\newsmart)
4. انتظر انتهاء التثبيت
5. شغل البرنامج من قائمة ابدأ أو سطح المكتب

### الطريقة الثانية: النسخة المحمولة
1. شغل ملف "newsmart-portable.exe" مباشرة
2. لا يحتاج تثبيت
3. يمكن تشغيله من أي مكان (USB، مجلد، إلخ)

## أول تشغيل:
1. عند أول تشغيل، سيطلب منك إنشاء شركة جديدة
2. أدخل بيانات الشركة المطلوبة
3. اختر العملة الافتراضية
4. أنشئ حساب المدير الأول

## الدعم الفني:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966-XX-XXX-XXXX
- الموقع: www.newsmart.com

## ملاحظات مهمة:
- احتفظ بنسخة احتياطية من البيانات بانتظام
- لا تحذف مجلد البيانات في AppData
- تأكد من تشغيل البرنامج بصلاحيات المدير عند الحاجة
```

### إعداد نص التثبيت التلقائي:
```batch
@echo off
echo ===================================
echo    تثبيت نظام newsmart المحاسبي
echo ===================================
echo.

echo جاري فحص متطلبات النظام...
timeout /t 2 >nul

echo جاري تثبيت البرنامج...
newsmart-setup.exe /S

echo.
echo تم التثبيت بنجاح!
echo يمكنك الآن تشغيل البرنامج من قائمة ابدأ
echo.
pause
```

### إعداد ملف إلغاء التثبيت:
```batch
@echo off
echo ===================================
echo   إلغاء تثبيت نظام newsmart المحاسبي
echo ===================================
echo.

echo تحذير: سيتم حذف البرنامج وجميع الإعدادات
echo هل تريد الاحتفاظ بالبيانات؟ (Y/N)
set /p choice=

if /i "%choice%"=="N" (
    echo جاري حذف البيانات...
    rmdir /s /q "%APPDATA%\newsmart"
)

echo جاري إلغاء التثبيت...
"%PROGRAMFILES%\newsmart\Uninstall newsmart.exe"

echo تم إلغاء التثبيت بنجاح!
pause
```

---

## 🔧 18. استكشاف الأخطاء وحلولها

### مشاكل البناء الشائعة:

#### خطأ: "Module not found"
```bash
# الحل:
rm -rf node_modules package-lock.json
npm install
npm run build
```

#### خطأ: "electron-builder failed"
```bash
# تحقق من:
1. وجود ملف package.json صحيح
2. وجود مجلد build مع الأيقونات
3. صحة مسارات الملفات في extraResources

# إعادة بناء:
npm run clean
npm install
npm run electron:build
```

#### خطأ: "Icon file not found"
```bash
# تأكد من وجود الأيقونات:
ls build/icons/
# يجب أن تحتوي على:
# - newsmart.ico
# - installerIcon.ico
# - uninstallerIcon.ico
```

#### خطأ: "Code signing failed"
```bash
# إذا لم تكن تريد التوقيع:
# أضف في package.json:
"win": {
  "sign": false
}
```

### مشاكل التشغيل للعملاء:

#### "التطبيق لا يبدأ"
```text
الحلول:
1. تشغيل كمدير (Run as Administrator)
2. تحقق من Windows Defender/Antivirus
3. تثبيت Visual C++ Redistributable
4. تحديث Windows
```

#### "قاعدة البيانات لا تعمل"
```text
الحلول:
1. تحقق من صلاحيات الكتابة في مجلد AppData
2. إغلاق جميع نسخ البرنامج
3. حذف ملف قاعدة البيانات المعطوب وإعادة إنشاؤه
```

#### "الخطوط لا تظهر بشكل صحيح"
```text
الحلول:
1. تثبيت خطوط عربية إضافية
2. تغيير إعدادات العرض في Windows
3. تحديث برامج تشغيل كرت الشاشة
```

---

## 📋 19. قائمة مراجعة ما قبل التوزيع

### فحص التطبيق:
- [ ] التطبيق يبدأ بدون أخطاء
- [ ] جميع الصفحات تعمل بشكل صحيح
- [ ] قاعدة البيانات تحفظ وتسترجع البيانات
- [ ] الطباعة تعمل بشكل صحيح
- [ ] النسخ الاحتياطي والاستعادة يعملان
- [ ] نظام الترخيص يعمل (إن وجد)
- [ ] الثيم والألوان تظهر بشكل صحيح

### فحص ملفات التوزيع:
- [ ] ملف exe يعمل على أجهزة مختلفة
- [ ] المثبت ينشئ الاختصارات بشكل صحيح
- [ ] إلغاء التثبيت يعمل بشكل كامل
- [ ] الأيقونات تظهر في جميع الأماكن
- [ ] ملفات المساعدة والتوثيق موجودة

### فحص الأمان:
- [ ] لا توجد كلمات مرور مكشوفة في الكود
- [ ] ملفات الإعداد محمية
- [ ] البيانات الحساسة مشفرة
- [ ] التطبيق لا يتطلب صلاحيات مدير غير ضرورية

---

---

## 🤖 20. أتمتة عملية البناء والتوزيع

### إنشاء سكريبت البناء التلقائي:
```batch
@echo off
title بناء وتوزيع نظام newsmart المحاسبي
color 0A

echo ========================================
echo    بناء وتوزيع نظام newsmart المحاسبي
echo ========================================
echo.

echo [1/8] تنظيف المشروع...
call npm run clean
if errorlevel 1 goto error

echo [2/8] تثبيت التبعيات...
call npm install
if errorlevel 1 goto error

echo [3/8] فحص الكود...
call npm run lint
if errorlevel 1 goto error

echo [4/8] تشغيل الاختبارات...
call npm test
if errorlevel 1 goto error

echo [5/8] بناء التطبيق...
call npm run build
if errorlevel 1 goto error

echo [6/8] إنشاء ملف exe...
call npm run electron:build:win
if errorlevel 1 goto error

echo [7/8] إنشاء حزمة التوزيع...
call build-distribution.bat
if errorlevel 1 goto error

echo [8/8] اختبار الملف النهائي...
call test-executable.bat
if errorlevel 1 goto error

echo.
echo ========================================
echo تم البناء والتوزيع بنجاح! ✅
echo ========================================
echo.
echo الملفات الجاهزة في مجلد: distribution/
echo - newsmart-setup.exe (المثبت)
echo - newsmart-portable.exe (النسخة المحمولة)
echo - README-العميل.txt (دليل العميل)
echo.
pause
goto end

:error
echo.
echo ========================================
echo حدث خطأ أثناء البناء! ❌
echo ========================================
echo.
pause

:end
```

### إنشاء سكريبت حزمة التوزيع:
```batch
@echo off
REM build-distribution.bat

echo إنشاء مجلد التوزيع...
if not exist "distribution" mkdir distribution
cd distribution

echo نسخ ملفات التثبيت...
copy "..\dist\newsmart Setup *.exe" "newsmart-setup.exe"
copy "..\dist\newsmart *.exe" "newsmart-portable.exe"

echo إنشاء ملفات المساعدة...
echo # دليل تثبيت وتشغيل نظام newsmart المحاسبي > README-العميل.txt
echo. >> README-العميل.txt
echo ## متطلبات النظام: >> README-العميل.txt
echo - نظام التشغيل: Windows 10/11 (64-bit) >> README-العميل.txt
echo - الذاكرة: 4 GB RAM كحد أدنى >> README-العميل.txt
echo - مساحة القرص: 500 MB مساحة فارغة >> README-العميل.txt
echo. >> README-العميل.txt
echo ## طريقة التثبيت: >> README-العميل.txt
echo 1. شغل ملف newsmart-setup.exe >> README-العميل.txt
echo 2. اتبع تعليمات المثبت >> README-العميل.txt
echo 3. شغل البرنامج من قائمة ابدأ >> README-العميل.txt

echo إنشاء سكريبت التثبيت السريع...
echo @echo off > تثبيت-سريع.bat
echo echo تثبيت نظام newsmart المحاسبي... >> تثبيت-سريع.bat
echo newsmart-setup.exe /S >> تثبيت-سريع.bat
echo echo تم التثبيت بنجاح! >> تثبيت-سريع.bat
echo pause >> تثبيت-سريع.bat

echo إنشاء معلومات الإصدار...
echo الإصدار: %date% > معلومات-الإصدار.txt
echo الوقت: %time% >> معلومات-الإصدار.txt
echo الحجم: >> معلومات-الإصدار.txt
dir *.exe >> معلومات-الإصدار.txt

cd ..
echo تم إنشاء حزمة التوزيع بنجاح!
```

### إنشاء سكريبت اختبار الملف التنفيذي:
```batch
@echo off
REM test-executable.bat

echo اختبار الملف التنفيذي...

echo فحص وجود الملفات...
if not exist "dist\newsmart Setup *.exe" (
    echo خطأ: ملف المثبت غير موجود!
    exit /b 1
)

echo فحص حجم الملفات...
for %%f in ("dist\newsmart Setup *.exe") do (
    if %%~zf LSS 50000000 (
        echo تحذير: حجم الملف صغير جداً (أقل من 50MB)
    )
)

echo اختبار تشغيل سريع...
timeout /t 3 >nul

echo جميع الاختبارات نجحت!
```

### إعداد GitHub Actions للبناء التلقائي:
```yaml
# .github/workflows/build.yml
name: Build and Release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:

jobs:
  build:
    runs-on: windows-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run tests
      run: npm test

    - name: Build application
      run: npm run build

    - name: Build executable
      run: npm run electron:build:win

    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: newsmart-windows
        path: |
          dist/*.exe
          dist/*.zip

    - name: Create Release
      if: startsWith(github.ref, 'refs/tags/')
      uses: softprops/action-gh-release@v1
      with:
        files: |
          dist/*.exe
          dist/*.zip
        draft: false
        prerelease: false
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
```

### إعداد سكريبت النشر للخادم:
```bash
#!/bin/bash
# deploy.sh

echo "نشر التطبيق على الخادم..."

# رفع الملفات إلى الخادم
scp -r distribution/ user@server:/var/www/downloads/newsmart/

# تحديث صفحة التحميل
ssh user@server "cd /var/www && ./update-download-page.sh"

# إرسال إشعار
curl -X POST "https://api.telegram.org/bot$BOT_TOKEN/sendMessage" \
     -d chat_id="$CHAT_ID" \
     -d text="تم نشر إصدار جديد من newsmart ✅"

echo "تم النشر بنجاح!"
```

---

## 📊 21. مراقبة الأداء والتحليلات

### إضافة تتبع الاستخدام:
```typescript
// src/utils/analytics.ts
export class Analytics {
  private static isEnabled = true;

  static trackEvent(event: string, properties?: any) {
    if (!this.isEnabled) return;

    const data = {
      event,
      properties: {
        ...properties,
        timestamp: new Date().toISOString(),
        version: process.env.npm_package_version,
        platform: process.platform
      }
    };

    // إرسال للخادم أو حفظ محلياً
    this.sendToServer(data);
  }

  static trackPageView(page: string) {
    this.trackEvent('page_view', { page });
  }

  static trackError(error: Error, context?: string) {
    this.trackEvent('error', {
      message: error.message,
      stack: error.stack,
      context
    });
  }

  private static async sendToServer(data: any) {
    try {
      // إرسال البيانات للخادم
      await fetch('https://analytics.newsmart.com/track', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
    } catch (e) {
      // تجاهل أخطاء التتبع
    }
  }
}
```

### إضافة تقارير الاستخدام:
```vue
<!-- src/pages/Admin/UsageReport.vue -->
<template>
  <div class="usage-report">
    <PageHeader :title="t`تقرير الاستخدام`" />

    <div class="p-6 space-y-6">
      <!-- إحصائيات عامة -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="stat-card">
          <h3>{{ t`إجمالي المستخدمين` }}</h3>
          <p class="stat-number">{{ stats.totalUsers }}</p>
        </div>
        <div class="stat-card">
          <h3>{{ t`الجلسات اليوم` }}</h3>
          <p class="stat-number">{{ stats.todaySessions }}</p>
        </div>
        <div class="stat-card">
          <h3>{{ t`الصفحات الأكثر زيارة` }}</h3>
          <p class="stat-number">{{ stats.topPage }}</p>
        </div>
        <div class="stat-card">
          <h3>{{ t`متوسط وقت الجلسة` }}</h3>
          <p class="stat-number">{{ stats.avgSessionTime }}</p>
        </div>
      </div>

      <!-- رسم بياني للاستخدام -->
      <div class="content-card p-6">
        <h3 class="text-lg font-semibold mb-4">{{ t`استخدام النظام خلال الأسبوع` }}</h3>
        <canvas ref="usageChart" width="800" height="400"></canvas>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'UsageReport',
  data() {
    return {
      stats: {
        totalUsers: 0,
        todaySessions: 0,
        topPage: '',
        avgSessionTime: '0 دقيقة'
      }
    };
  },
  async mounted() {
    await this.loadStats();
    this.drawChart();
  },
  methods: {
    async loadStats() {
      // تحميل إحصائيات الاستخدام
      try {
        const response = await fetch('/api/usage-stats');
        this.stats = await response.json();
      } catch (error) {
        console.error('Error loading stats:', error);
      }
    },

    drawChart() {
      // رسم الرسم البياني
      const canvas = this.$refs.usageChart as HTMLCanvasElement;
      const ctx = canvas.getContext('2d');

      // رسم بيانات الاستخدام
      // ... كود الرسم البياني
    }
  }
});
</script>
```

---

## 🔄 22. التحديثات التلقائية

### إعداد نظام التحديث:
```typescript
// src/utils/updater.ts
import { autoUpdater } from 'electron-updater';
import { dialog } from 'electron';

export class AppUpdater {
  constructor() {
    autoUpdater.checkForUpdatesAndNotify();
    this.setupEventHandlers();
  }

  private setupEventHandlers() {
    autoUpdater.on('checking-for-update', () => {
      console.log('Checking for update...');
    });

    autoUpdater.on('update-available', (info) => {
      dialog.showMessageBox({
        type: 'info',
        title: 'تحديث متوفر',
        message: `إصدار جديد متوفر: ${info.version}`,
        detail: 'سيتم تحميل التحديث في الخلفية.',
        buttons: ['موافق']
      });
    });

    autoUpdater.on('update-not-available', (info) => {
      console.log('Update not available.');
    });

    autoUpdater.on('error', (err) => {
      console.error('Error in auto-updater:', err);
    });

    autoUpdater.on('download-progress', (progressObj) => {
      let log_message = "Download speed: " + progressObj.bytesPerSecond;
      log_message = log_message + ' - Downloaded ' + progressObj.percent + '%';
      log_message = log_message + ' (' + progressObj.transferred + "/" + progressObj.total + ')';
      console.log(log_message);
    });

    autoUpdater.on('update-downloaded', (info) => {
      dialog.showMessageBox({
        type: 'info',
        title: 'تحديث جاهز',
        message: 'تم تحميل التحديث بنجاح',
        detail: 'سيتم إعادة تشغيل التطبيق لتطبيق التحديث.',
        buttons: ['إعادة التشغيل الآن', 'لاحقاً']
      }).then((result) => {
        if (result.response === 0) {
          autoUpdater.quitAndInstall();
        }
      });
    });
  }

  checkForUpdates() {
    autoUpdater.checkForUpdatesAndNotify();
  }
}
```

### إعداد خادم التحديثات:
```json
// update-server/latest.yml
version: 1.0.0
files:
  - url: newsmart-setup.exe
    sha512: [hash]
    size: 52428800
path: newsmart-setup.exe
sha512: [hash]
releaseDate: '2024-12-17T10:00:00.000Z'
```

---

**تم إنشاء هذا الدليل الشامل لمساعدة المطورين في فهم وتطوير النظام المحاسبي بشكل احترافي** 🚀

**آخر تحديث:** 2024-12-17
**الإصدار:** 4.0.0
