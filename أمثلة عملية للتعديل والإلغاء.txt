# أمثلة عملية للتعديل والإلغاء - دليل سريع

## 🚀 أمثلة سريعة للمهام الشائعة

---

## 1️⃣ إلغاء الثيم السماوي والعودة للأصلي

### **الطريقة السريعة (30 ثانية):**
```javascript
// افتح وحدة التحكم (F12) واكتب:
localStorage.setItem('selectedTheme', 'modern');
window.location.reload();
```

### **الطريقة من الواجهة (1 دقيقة):**
```
1. انقر على زر الثيم في أسفل الشريط الجانبي
2. اختر "Modern Blue" 
3. تم! النظام عاد للثيم الأصلي
```

### **الطريقة من الكود (5 دقائق):**
```typescript
// في src/utils/theme.ts غير السطر 95:
let currentTheme: string = 'modern'; // بدلاً من 'skyBlue'

// وغير السطر 143:
const savedTheme = localStorage.getItem('selectedTheme') || 'modern';
```

---

## 2️⃣ إظهار مديول المخزون للجميع

### **المشكلة:** مديول المخزون مخفي عن بعض المستخدمين

### **الحل السريع:**
```typescript
// في src/utils/sidebarConfig.ts
// ابحث عن "Inventory" وغير:
hidden: () => {
  // كود معقد هنا...
},
// إلى:
hidden: () => false,
```

### **الحل المتقدم (إظهار للأدوار المحددة):**
```typescript
hidden: () => {
  const userRole = localStorage.getItem('userRole');
  const allowedRoles = ['Admin', 'Inventory_Manager', 'Sales_Manager'];
  return !allowedRoles.includes(userRole);
},
```

---

## 3️⃣ تغيير لون الثيم السماوي

### **تغيير إلى اللون الأخضر:**
```css
/* في src/styles/theme.css ابحث عن [data-theme="skyBlue"] وغير: */
[data-theme="skyBlue"] {
  --sidebar-bg: #166534;        /* أخضر داكن */
  --sidebar-text: #DCFCE7;      /* أخضر فاتح */
  --content-bg: #F0FDF4;        /* أخضر فاتح جداً */
  --text-primary: #166534;      /* أخضر داكن */
  --text-accent: #22C55E;       /* أخضر مشرق */
}
```

### **تغيير إلى اللون البنفسجي:**
```css
[data-theme="skyBlue"] {
  --sidebar-bg: #7C3AED;        /* بنفسجي داكن */
  --sidebar-text: #F3E8FF;      /* بنفسجي فاتح */
  --content-bg: #FAF5FF;        /* بنفسجي فاتح جداً */
  --text-primary: #7C3AED;      /* بنفسجي داكن */
  --text-accent: #A855F7;       /* بنفسجي مشرق */
}
```

---

## 4️⃣ إضافة مديول جديد بسرعة

### **مثال: مديول إدارة الموظفين**

#### **الخطوة 1: إنشاء الصفحة**
```vue
<!-- src/pages/Employees.vue -->
<template>
  <div class="employees-page">
    <PageHeader :title="t`إدارة الموظفين`" />
    <div class="dashboard-card">
      <h2>قائمة الموظفين</h2>
      <p>هنا يمكنك إدارة بيانات الموظفين</p>
      <!-- أضف المحتوى هنا -->
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import PageHeader from 'src/components/PageHeader.vue';

export default defineComponent({
  name: 'Employees',
  components: { PageHeader },
});
</script>
```

#### **الخطوة 2: إضافة المسار**
```typescript
// في src/router.ts أضف:
import Employees from 'src/pages/Employees.vue';

// وأضف في routes:
{
  path: '/employees',
  name: 'Employees',
  component: Employees,
  meta: { requiresAuth: true }
},
```

#### **الخطوة 3: إضافة في الشريط الجانبي**
```typescript
// في src/utils/sidebarConfig.ts أضف:
{
  label: t`إدارة الموظفين`,
  name: 'employees',
  route: '/employees',
  icon: 'users',
  hidden: () => {
    const userRole = localStorage.getItem('userRole');
    return userRole !== 'Admin'; // للمدير فقط
  },
},
```

---

## 5️⃣ إخفاء مديولات غير مرغوبة

### **إخفاء مديول المبيعات:**
```typescript
// في src/utils/sidebarConfig.ts ابحث عن sales وغير:
hidden: () => true, // سيخفي المديول نهائياً
```

### **إخفاء مديول التقارير من المستخدمين العاديين:**
```typescript
hidden: () => {
  const userRole = localStorage.getItem('userRole');
  return userRole === 'User'; // مخفي للمستخدمين العاديين فقط
},
```

---

## 6️⃣ تغيير أسماء المديولات

### **تغيير "المبيعات" إلى "نقاط البيع":**
```typescript
// في src/utils/sidebarConfig.ts ابحث عن:
label: t`المبيعات`,
// وغيرها إلى:
label: t`نقاط البيع`,
```

### **تغيير "المخزون" إلى "إدارة المخازن":**
```typescript
label: t`إدارة المخازن`, // بدلاً من "المخزون"
```

---

## 7️⃣ إضافة أيقونات جديدة

### **قائمة الأيقونات المتاحة:**
```
- users (مستخدمين)
- shopping-cart (عربة تسوق)
- package (صندوق)
- file-text (ملف نصي)
- bar-chart (رسم بياني)
- settings (إعدادات)
- home (منزل)
- star (نجمة)
- heart (قلب)
- grid (شبكة)
- folder (مجلد)
- cloud (سحابة)
- database (قاعدة بيانات)
- credit-card (بطاقة ائتمان)
- truck (شاحنة)
```

### **تغيير أيقونة مديول:**
```typescript
icon: 'database', // غير الأيقونة هنا
```

---

## 8️⃣ حل المشاكل الشائعة

### **المشكلة: الثيم لا يتغير**
```bash
# الحلول بالترتيب:
1. امسح cache: Ctrl+Shift+Delete
2. أعد تشغيل: npm run dev
3. امسح localStorage: localStorage.clear()
4. تحقق من الأخطاء: F12 > Console
```

### **المشكلة: المديول لا يظهر**
```typescript
// تحقق من:
1. hidden: () => false  ✓
2. المسار في router.ts ✓
3. الملف موجود ✓
4. لا توجد أخطاء في Console ✓
```

### **المشكلة: خطأ في CSS**
```bash
# الحلول:
1. تحقق من استيراد الملف في index.css
2. أعد تشغيل الخادم
3. تحقق من أخطاء PostCSS
```

---

## 9️⃣ نسخ احتياطية سريعة

### **نسخة احتياطية سريعة:**
```bash
# انسخ الملفات المهمة
cp src/utils/theme.ts theme_backup.ts
cp src/utils/sidebarConfig.ts sidebar_backup.ts
cp src/styles/theme.css theme_styles_backup.css
```

### **استعادة سريعة:**
```bash
# استعد الملفات
cp theme_backup.ts src/utils/theme.ts
cp sidebar_backup.ts src/utils/sidebarConfig.ts
cp theme_styles_backup.css src/styles/theme.css
```

### **باستخدام Git:**
```bash
# حفظ التغييرات
git add .
git commit -m "نسخة احتياطية"

# استعادة
git reset --hard HEAD~1
```

---

## 🔟 اختصارات مفيدة

### **أوامر سريعة:**
```bash
# إعادة تشغيل الخادم
npm run dev

# فحص الأخطاء
npm run lint

# مسح cache npm
npm cache clean --force

# عرض حالة Git
git status

# عرض آخر 5 commits
git log --oneline -5
```

### **اختصارات المتصفح:**
```
F12 - فتح أدوات المطور
Ctrl+Shift+Delete - مسح cache
Ctrl+Shift+R - إعادة تحميل بدون cache
Ctrl+Shift+I - فتح أدوات المطور
F5 - إعادة تحميل الصفحة
```

---

## 1️⃣1️⃣ قوالب جاهزة للنسخ

### **قالب مديول جديد:**
```typescript
// في sidebarConfig.ts
{
  label: t`اسم المديول`,
  name: 'module-name',
  route: '/module-route',
  icon: 'icon-name',
  hidden: () => {
    const userRole = localStorage.getItem('userRole');
    return userRole !== 'Admin';
  },
},
```

### **قالب ثيم جديد:**
```typescript
// في theme.ts
newTheme: {
  name: 'اسم الثيم',
  colors: {
    sidebar: 'bg-color-800',
    content: 'bg-color-50',
    primary: 'bg-color-600',
    secondary: 'bg-color-500',
    accent: 'bg-color-400'
  },
  darkMode: false
},
```

```css
/* في theme.css */
[data-theme="newTheme"] {
  --sidebar-bg: #color;
  --sidebar-text: #color;
  --content-bg: #color;
  --text-primary: #color;
  --text-accent: #color;
}
```

### **قالب صفحة جديدة:**
```vue
<template>
  <div class="page-name">
    <PageHeader :title="t`عنوان الصفحة`" />
    <div class="dashboard-card">
      <h2>محتوى الصفحة</h2>
      <!-- المحتوى هنا -->
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import PageHeader from 'src/components/PageHeader.vue';

export default defineComponent({
  name: 'PageName',
  components: { PageHeader },
});
</script>
```

---

## 🎯 نصائح للتعديل الآمن

### **قبل أي تعديل:**
1. **اقرأ الكود** الموجود أولاً
2. **افهم التأثير** المتوقع
3. **انشئ نسخة احتياطية**
4. **اختبر في بيئة التطوير**

### **أثناء التعديل:**
1. **عدل ملف واحد** في كل مرة
2. **احفظ واختبر** بعد كل تغيير
3. **راقب رسائل الأخطاء**
4. **استخدم Git** لحفظ التقدم

### **بعد التعديل:**
1. **اختبر جميع الوظائف**
2. **تأكد من عدم وجود أخطاء**
3. **وثق التغييرات**
4. **انشئ commit نهائي**

---

**هذا الدليل السريع يساعدك في تنفيذ التعديلات الشائعة بسرعة وأمان!** ⚡🔧✨

**تاريخ الإنشاء:** 2024-12-16
**آخر تحديث:** 2024-12-16
