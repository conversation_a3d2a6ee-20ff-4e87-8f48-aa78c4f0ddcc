const { app, BrowserWindow, ipcMain, dialog, clipboard } = require('electron');
const path = require('path');
const fs = require('fs');
const os = require('os');
const crypto = require('crypto');

// متغيرات عامة
let mainWindow;
let licenseHistory = [];

// تعطيل تحذيرات الأمان في وضع التطوير
process.env['ELECTRON_DISABLE_SECURITY_WARNINGS'] = 'true';

// إنشاء النافذة الرئيسية
function createWindow() {
  mainWindow = new BrowserWindow({
    width: 900,
    height: 700,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    icon: path.join(__dirname, '../assets/icon.png'),
    title: 'مولد التراخيص - أداة المطور',
    resizable: true,
    minimizable: true,
    maximizable: true,
    show: false
  });

  // تحميل الصفحة الرئيسية
  mainWindow.loadFile(path.join(__dirname, 'index.html'));

  // إظهار النافذة عند الانتهاء من التحميل
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // تحميل سجل التراخيص
    loadLicenseHistory();
  });

  // إغلاق التطبيق عند إغلاق النافذة
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // فتح أدوات المطور في وضع التطوير
  if (process.argv.includes('--dev')) {
    mainWindow.webContents.openDevTools();
  }
}

// كلمة السر السرية - يجب أن تطابق النظام الأساسي تماماً
const SECRET_KEY = 'NEWSMART_LICENSE_SECRET_2024_MONEER_ALSHAWEA';

// دالة إنشاء الرقم التسلسلي - محدثة مع كلمة السر السرية
function generateSerialNumber() {
  try {
    const networkInterfaces = os.networkInterfaces();
    const cpus = os.cpus();
    const platform = os.platform();
    const arch = os.arch();

    // جمع معلومات الجهاز مع كلمة السر السرية
    const machineInfo = {
      platform,
      arch,
      cpuModel: cpus[0]?.model || '',
      totalMemory: os.totalmem(),
      hostname: os.hostname(),
      userInfo: os.userInfo().username,
      secret: SECRET_KEY // إضافة كلمة السر السرية
    };

    // جمع عناوين MAC
    const macAddresses = [];
    if (networkInterfaces && typeof networkInterfaces === 'object') {
      Object.values(networkInterfaces).forEach((interfaces) => {
        if (Array.isArray(interfaces)) {
          interfaces.forEach((iface) => {
            if (iface && iface.mac && iface.mac !== '00:00:00:00:00:00') {
              macAddresses.push(iface.mac);
            }
          });
        }
      });
    }

    // أخذ أول عنوان MAC كمعرف أساسي
    const primaryMac = macAddresses.sort()[0] || 'NO-MAC';
    machineInfo.primaryMac = primaryMac;

    // إنشاء hash للمعلومات مع كلمة السر السرية
    const hash = crypto.createHash('sha256');
    hash.update(JSON.stringify(machineInfo));
    const fullHash = hash.digest('hex');

    // تنسيق الرقم التسلسلي: XXXX-XXXX-XXXX-XXXX
    const serialParts = [
      fullHash.substring(0, 4).toUpperCase(),
      fullHash.substring(4, 8).toUpperCase(),
      fullHash.substring(8, 12).toUpperCase(),
      fullHash.substring(12, 16).toUpperCase(),
    ];

    const serialNumber = serialParts.join('-');

    // تسجيل معلومات التشخيص
    console.log('=== معلومات إنشاء الرقم التسلسلي ===');
    console.log('المنصة:', platform);
    console.log('المعمارية:', arch);
    console.log('اسم الجهاز:', os.hostname());
    console.log('المستخدم:', os.userInfo().username);
    console.log('عناوين MAC:', macAddresses);
    console.log('المعالج:', cpus[0]?.model || 'غير معروف');
    console.log('الذاكرة (GB):', Math.floor(os.totalmem() / (1024 * 1024 * 1024)));
    console.log('الرقم التسلسلي:', serialNumber);
    console.log('=====================================');

    return serialNumber;
  } catch (error) {
    console.error('Error generating serial number:', error);
    // إرجاع رقم تسلسلي افتراضي في حالة الخطأ مع كلمة السر السرية
    const fallbackData = {
      timestamp: Date.now(),
      random: Math.random(),
      secret: SECRET_KEY
    };
    const hash = crypto.createHash('sha256');
    hash.update(JSON.stringify(fallbackData));
    const fallbackHash = hash.digest('hex');

    return `ERR-${fallbackHash.substring(0, 4).toUpperCase()}-${fallbackHash.substring(4, 8).toUpperCase()}-${fallbackHash.substring(8, 12).toUpperCase()}`;
  }
}

// دالة إنشاء مفتاح الترخيص - الكود الأصلي
// function generateLicenseKey(serialNumber, licenseType = 'trial', durationDays = 7) {
//   try {
//     // إزالة الشرطات من الرقم التسلسلي
//     const cleanSerial = serialNumber.replace(/-/g, '');
//
//     // إنشاء بيانات الترخيص
//     const licenseData = {
//       serial: cleanSerial,
//       type: licenseType,
//       duration: durationDays,
//       timestamp: Date.now(),
//     };
//
//     // إنشاء hash للبيانات
//     const hash = crypto.createHash('sha256');
//     hash.update(JSON.stringify(licenseData));
//     const licenseHash = hash.digest('hex');
//
//     // تنسيق مفتاح الترخيص
//     let prefix = '';
//     switch (licenseType) {
//       case 'trial':
//         prefix = 'TRL';
//         break;
//       case 'permanent':
//         prefix = 'PRM';
//         break;
//       default:
//         prefix = 'GEN';
//     }
//
//     // تكوين المفتاح: PREFIX-XXXX-XXXX-XXXX-XXXX
//     const keyParts = [
//       prefix,
//       licenseHash.substring(0, 4).toUpperCase(),
//       licenseHash.substring(4, 8).toUpperCase(),
//       licenseHash.substring(8, 12).toUpperCase(),
//       licenseHash.substring(12, 16).toUpperCase(),
//     ];
//
//     return keyParts.join('-');
//   } catch (error) {
//     console.error('Error generating license key:', error);
//     throw new Error('فشل في إنشاء مفتاح الترخيص');
//   }
// }

// دالة إنشاء مفتاح الترخيص - الكود المحدث مع كلمة السر السرية
function generateLicenseKey(serialNumber, licenseType = 'trial', durationDays = 7) {
  try {
    // إزالة الشرطات من الرقم التسلسلي
    const cleanSerial = serialNumber.replace(/-/g, '');

    // إنشاء بيانات الترخيص مع كلمة السر السرية (مطابق للنظام الأساسي)
    const licenseData = {
      serial: cleanSerial,
      type: licenseType,
      secret: SECRET_KEY // إضافة كلمة السر السرية
    };

    // إنشاء hash للبيانات مع كلمة السر السرية
    const hash = crypto.createHash('sha256');
    hash.update(JSON.stringify(licenseData));
    const licenseHash = hash.digest('hex');

    // تنسيق مفتاح الترخيص
    let prefix = '';
    switch (licenseType) {
      case 'trial':
        prefix = 'TRL';
        break;
      case 'permanent':
        prefix = 'PRM';
        break;
      default:
        prefix = 'GEN';
    }

    // تكوين المفتاح: PREFIX-XXXX-XXXX-XXXX-XXXX
    const keyParts = [
      prefix,
      licenseHash.substring(0, 4).toUpperCase(),
      licenseHash.substring(4, 8).toUpperCase(),
      licenseHash.substring(8, 12).toUpperCase(),
      licenseHash.substring(12, 16).toUpperCase(),
    ];

    const licenseKey = keyParts.join('-');

    // تسجيل معلومات التشخيص
    console.log('=== معلومات إنشاء مفتاح الترخيص ===');
    console.log('الرقم التسلسلي:', serialNumber);
    console.log('الرقم المنظف:', cleanSerial);
    console.log('نوع الترخيص:', licenseType);
    console.log('مفتاح الترخيص:', licenseKey);
    console.log('==========================================');

    return licenseKey;
  } catch (error) {
    console.error('Error generating license key:', error);
    throw new Error('فشل في إنشاء مفتاح الترخيص');
  }
}

// دالة تحميل سجل التراخيص
function loadLicenseHistory() {
  try {
    const historyPath = path.join(os.homedir(), 'license-generator-history.json');
    if (fs.existsSync(historyPath)) {
      const data = fs.readFileSync(historyPath, 'utf8');
      licenseHistory = JSON.parse(data);
    }
  } catch (error) {
    console.error('Error loading license history:', error);
    licenseHistory = [];
  }
}

// دالة حفظ سجل التراخيص
function saveLicenseHistory() {
  try {
    const historyPath = path.join(os.homedir(), 'license-generator-history.json');
    // الاحتفاظ بآخر 100 ترخيص فقط
    const historyToSave = licenseHistory.slice(0, 100);
    fs.writeFileSync(historyPath, JSON.stringify(historyToSave, null, 2));
  } catch (error) {
    console.error('Error saving license history:', error);
  }
}

// معالجات IPC
// دالة اختبار للتحقق من عمل الخوارزمية - للمطور فقط
function testLicenseGeneration(serialNumber) {
  try {
    // إنشاء مفاتيح اختبار
    const trialKey = generateLicenseKey(serialNumber, 'trial');
    const permanentKey = generateLicenseKey(serialNumber, 'permanent');

    console.log('=== اختبار خوارزمية الترخيص في أداة الإنشاء ===');
    console.log('الرقم التسلسلي:', serialNumber);
    console.log('مفتاح تجريبي:', trialKey);
    console.log('مفتاح دائم:', permanentKey);

    return {
      serialNumber,
      trialKey,
      permanentKey
    };
  } catch (error) {
    console.error('خطأ في اختبار الخوارزمية:', error);
    throw error;
  }
}

ipcMain.handle('get-serial-number', () => {
  return generateSerialNumber();
});

ipcMain.handle('test-license-generation', (event, serialNumber) => {
  try {
    return { success: true, result: testLicenseGeneration(serialNumber) };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('generate-license', (event, data) => {
  try {
    const { serialNumber, licenseType, duration, clientName, notes } = data;
    
    // إنشاء مفتاح الترخيص
    const licenseKey = generateLicenseKey(serialNumber, licenseType, duration);
    
    // إنشاء كائن الترخيص
    const license = {
      key: licenseKey,
      serialNumber: serialNumber,
      type: licenseType,
      duration: duration,
      clientName: clientName || '',
      notes: notes || '',
      createdAt: new Date().toISOString(),
    };
    
    // إضافة إلى السجل
    licenseHistory.unshift(license);
    saveLicenseHistory();
    
    return { success: true, license };
  } catch (error) {
    console.error('Error generating license:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-license-history', () => {
  return licenseHistory;
});

ipcMain.handle('copy-to-clipboard', (event, text) => {
  try {
    clipboard.writeText(text);
    return { success: true };
  } catch (error) {
    console.error('Error copying to clipboard:', error);
    return { success: false, error: error.message };
  }
});

// معالج حفظ سجل التراخيص
ipcMain.handle('save-license-history', async (event, history) => {
  try {
    licenseHistory = history; // تحديث المتغير العام
    const historyPath = path.join(app.getPath('userData'), 'license-history.json');
    await fs.writeFile(historyPath, JSON.stringify(history, null, 2), 'utf8');
    return { success: true };
  } catch (error) {
    console.error('Error saving license history:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('export-history', async () => {
  try {
    const result = await dialog.showSaveDialog(mainWindow, {
      title: 'تصدير سجل التراخيص',
      defaultPath: 'license-history.json',
      filters: [
        { name: 'JSON Files', extensions: ['json'] },
        { name: 'All Files', extensions: ['*'] }
      ]
    });
    
    if (!result.canceled && result.filePath) {
      fs.writeFileSync(result.filePath, JSON.stringify(licenseHistory, null, 2));
      return { success: true, path: result.filePath };
    }
    
    return { success: false, error: 'تم إلغاء العملية' };
  } catch (error) {
    console.error('Error exporting history:', error);
    return { success: false, error: error.message };
  }
});

// أحداث التطبيق
app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
