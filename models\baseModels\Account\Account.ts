import { Fyo } from 'fyo';
import { Doc } from 'fyo/model/doc';
import {
  DefaultMap,
  FiltersMap,
  ListViewSettings,
  RequiredMap,
  TreeViewSettings,
  ReadOnlyMap,
  FormulaMap,
} from 'fyo/model/types';
import { ModelNameEnum } from 'models/types';
import { QueryFilter } from 'utils/db/types';
import { AccountRootType, AccountRootTypeEnum, AccountType } from './types';

export class Account extends Doc {
  rootType?: AccountRootType;
  accountType?: AccountType;
  parentAccount?: string;
  accountNumber?: string; // إضافة حقل رقم الحساب

  get isDebit() {
    if (this.rootType === AccountRootTypeEnum.Asset) {
      return true;
    }

    if (this.rootType === AccountRootTypeEnum.Expense) {
      return true;
    }

    return false;
  }

  get isCredit() {
    return !this.isDebit;
  }

  required: RequiredMap = {
    /**
     * Added here cause rootAccounts don't have parents
     * they are created during initialization. if this is
     * added to the schema it will cause NOT NULL errors
     */

    parentAccount: () => !!this.fyo.singles?.AccountingSettings?.setupComplete,
  };

  static defaults: DefaultMap = {
    /**
     * NestedSet indices are actually not used
     * this needs updation as they may be required
     * later on.
     */
    lft: () => 0,
    rgt: () => 0,
  };

  async beforeSync() {
    try {
      // إنشاء رقم الحساب التلقائي إذا لم يكن موجود
      if (!this.accountNumber) {
        this.accountNumber = await this.generateAccountNumber();
      }

      if (this.accountType || !this.parentAccount) {
        return;
      }

      const account = await this.fyo.db.get('Account', this.parentAccount);
      this.accountType = account.accountType as AccountType;
    } catch (error) {
      console.error('خطأ في beforeSync للحساب:', this.name, error);
      // في حالة الخطأ، تأكد من وجود رقم حساب على الأقل
      if (!this.accountNumber) {
        this.accountNumber = '9999';
      }
    }
  }

  /**
   * إنشاء رقم الحساب التلقائي بناءً على التسلسل الهرمي
   * نظام الترقيم:
   * - الحسابات الرئيسية: 1000, 2000, 3000, 4000, 5000
   * - الحسابات الفرعية: 1100, 1200, 1300...
   * - الحسابات الفرعية من المستوى الثاني: 1110, 1120, 1130...
   */
  async generateAccountNumber(): Promise<string> {
    try {
      // إذا كان حساب رئيسي (بدون والد)
      if (!this.parentAccount) {
        return await this.generateRootAccountNumber();
      }

      // إذا كان حساب فرعي
      return await this.generateChildAccountNumber();
    } catch (error) {
      console.error('خطأ في إنشاء رقم الحساب:', error);
      // في حالة الخطأ، إرجاع رقم افتراضي
      return '9999';
    }
  }

  /**
   * إنشاء رقم للحسابات الرئيسية
   */
  async generateRootAccountNumber(): Promise<string> {
    const rootTypeNumbers: Record<AccountRootType, string> = {
      [AccountRootTypeEnum.Asset]: '1000',
      [AccountRootTypeEnum.Liability]: '2000',
      [AccountRootTypeEnum.Equity]: '3000',
      [AccountRootTypeEnum.Income]: '4000',
      [AccountRootTypeEnum.Expense]: '5000',
    };

    const baseNumber = rootTypeNumbers[this.rootType as AccountRootType];
    if (!baseNumber) {
      return '9000'; // رقم افتراضي للأنواع غير المعروفة
    }

    // البحث عن أعلى رقم مستخدم في نفس النوع
    const existingAccounts = await this.fyo.db.getAll('Account', {
      filters: {
        rootType: this.rootType,
        parentAccount: null, // الحسابات الرئيسية فقط
      },
      fields: ['accountNumber'],
    });

    let maxNumber = parseInt(baseNumber);
    for (const account of existingAccounts) {
      if (account.accountNumber) {
        const num = parseInt(account.accountNumber);
        if (!isNaN(num) && num >= maxNumber) {
          maxNumber = num;
        }
      }
    }

    // إذا لم توجد حسابات، إرجاع الرقم الأساسي
    if (maxNumber === parseInt(baseNumber)) {
      return baseNumber;
    }

    // إضافة 1000 للرقم التالي
    return (maxNumber + 1000).toString();
  }

  /**
   * إنشاء رقم للحسابات الفرعية
   */
  async generateChildAccountNumber(): Promise<string> {
    try {
      // التأكد من وجود parentAccount
      if (!this.parentAccount) {
        console.error('لا يوجد حساب والد محدد');
        return '9999';
      }

      // الحصول على رقم الحساب الوالد
      const parentAccount = await this.fyo.db.get('Account', this.parentAccount);
      const parentNumber = parentAccount.accountNumber as string;

      if (!parentNumber) {
        console.error(`الحساب الوالد ${this.parentAccount} لا يحتوي على رقم`);
        return '9999'; // في حالة عدم وجود رقم للوالد
      }
    } catch (error) {
      console.error('خطأ في الحصول على الحساب الوالد:', error);
      return '9999';
    }

    // الحصول على رقم الحساب الوالد (مرة أخرى للتأكد)
    const parentAccount = await this.fyo.db.get('Account', this.parentAccount);
    const parentNumber = parentAccount.accountNumber as string;

    // البحث عن الحسابات الفرعية للوالد
    const siblingAccounts = await this.fyo.db.getAll('Account', {
      filters: {
        parentAccount: this.parentAccount,
      },
      fields: ['accountNumber'],
    });

    // تحديد نمط الترقيم بناءً على مستوى العمق
    const parentNumberInt = parseInt(parentNumber);
    let increment = 100; // للمستوى الأول من الفروع (1000 -> 1100, 1200...)

    // تحديد الزيادة بناءً على طول رقم الوالد
    if (parentNumber.length === 4) {
      // المستوى الأول: 1000 -> 1100, 1200, 1300...
      increment = 100;
    } else if (parentNumber.length === 5) {
      // المستوى الثاني: 1100 -> 1110, 1120, 1130...
      increment = 10;
    } else {
      // المستوى الثالث وما بعده: 1110 -> 1111, 1112, 1113...
      increment = 1;
    }

    // إذا لم توجد حسابات شقيقة، ابدأ من رقم الوالد + الزيادة
    if (siblingAccounts.length === 0) {
      return (parentNumberInt + increment).toString();
    }

    // البحث عن أعلى رقم مستخدم من الحسابات الشقيقة
    let maxNumber = parentNumberInt;
    for (const account of siblingAccounts) {
      if (account.accountNumber) {
        const num = parseInt(account.accountNumber);
        if (!isNaN(num) && num > maxNumber) {
          maxNumber = num;
        }
      }
    }

    // إنشاء الرقم التالي
    const nextNumber = maxNumber + increment;
    return nextNumber.toString();
  }

  static getListViewSettings(): ListViewSettings {
    return {
      // إضافة رقم الحساب في بداية الأعمدة لإظهار الترقيم
      columns: ['accountNumber', 'name', 'rootType', 'isGroup', 'parentAccount'],
    };
  }

  static getTreeSettings(fyo: Fyo): void | TreeViewSettings {
    return {
      parentField: 'parentAccount',
      async getRootLabel(): Promise<string> {
        const accountingSettings = await fyo.doc.getDoc('AccountingSettings');
        return accountingSettings.companyName as string;
      },
    };
  }

  formulas: FormulaMap = {
    rootType: {
      formula: async () => {
        if (!this.parentAccount) {
          return;
        }

        return await this.fyo.getValue(
          ModelNameEnum.Account,
          this.parentAccount,
          'rootType'
        );
      },
    },
  };

  static filters: FiltersMap = {
    parentAccount: (doc: Doc) => {
      const filter: QueryFilter = {
        isGroup: true,
      };

      if (doc?.rootType) {
        filter.rootType = doc.rootType as string;
      }

      return filter;
    },
  };

  readOnly: ReadOnlyMap = {
    rootType: () => this.inserted,
    parentAccount: () => this.inserted,
    accountType: () => !!this.accountType && this.inserted,
    isGroup: () => this.inserted,
    // جعل رقم الحساب للقراءة فقط لمنع التعديل اليدوي
    accountNumber: () => true,
  };

  /**
   * دالة ثابتة لإعادة ترقيم جميع الحسابات الموجودة
   * يمكن استخدامها لترقيم الحسابات التي تم إنشاؤها قبل إضافة هذه الميزة
   */
  static async renumberAllAccounts(fyo: Fyo): Promise<void> {
    try {
      console.log('🔢 بدء إعادة ترقيم جميع الحسابات...');

      // الحصول على جميع الحسابات مرتبة حسب التسلسل الهرمي
      const allAccounts = await fyo.db.getAll('Account', {
        fields: ['name', 'parentAccount', 'rootType', 'accountNumber'],
        orderBy: 'name',
        order: 'asc',
      });

      // ترقيم الحسابات الرئيسية أولاً
      const rootAccounts = allAccounts.filter(acc => !acc.parentAccount);
      for (const account of rootAccounts) {
        if (!account.accountNumber) {
          try {
            const doc = await fyo.doc.getDoc('Account', account.name);
            const newNumber = await (doc as Account).generateRootAccountNumber();
            // استخدام set بدلاً من setAndSync لتجنب التحقق من الحقول المطلوبة
            await doc.set({ accountNumber: newNumber });
            await doc.sync();
            console.log(`✅ تم ترقيم الحساب الرئيسي: ${account.name} -> ${newNumber}`);
          } catch (error) {
            console.error(`❌ خطأ في ترقيم الحساب الرئيسي ${account.name}:`, error);
          }
        }
      }

      // ترقيم الحسابات الفرعية - مرتبة حسب المستوى
      const childAccounts = allAccounts.filter(acc => acc.parentAccount);

      // ترتيب الحسابات الفرعية حسب عمق المستوى (الوالد أولاً)
      childAccounts.sort((a, b) => {
        const aDepth = this.getAccountDepth(a.name, allAccounts);
        const bDepth = this.getAccountDepth(b.name, allAccounts);
        return aDepth - bDepth;
      });

      for (const account of childAccounts) {
        if (!account.accountNumber) {
          try {
            const doc = await fyo.doc.getDoc('Account', account.name);

            // التأكد من أن الحساب الوالد له رقم
            const parentAccount = await fyo.db.get('Account', account.parentAccount);
            if (!parentAccount.accountNumber) {
              console.log(`⏭️ تخطي ${account.name} - الحساب الوالد ${account.parentAccount} لا يحتوي على رقم`);
              continue;
            }

            const newNumber = await (doc as Account).generateChildAccountNumber();
            // استخدام set بدلاً من setAndSync لتجنب التحقق من الحقول المطلوبة
            await doc.set({ accountNumber: newNumber });
            await doc.sync();
            console.log(`✅ تم ترقيم الحساب الفرعي: ${account.name} -> ${newNumber}`);
          } catch (error) {
            console.error(`❌ خطأ في ترقيم الحساب الفرعي ${account.name}:`, error);
          }
        }
      }

      console.log('🎉 تم الانتهاء من إعادة ترقيم جميع الحسابات بنجاح!');
    } catch (error) {
      console.error('❌ خطأ في إعادة ترقيم الحسابات:', error);
      throw error;
    }
  }

  /**
   * حساب عمق الحساب في الشجرة
   */
  private static getAccountDepth(accountName: string, allAccounts: any[]): number {
    const account = allAccounts.find(acc => acc.name === accountName);
    if (!account || !account.parentAccount) {
      return 0;
    }
    return 1 + this.getAccountDepth(account.parentAccount, allAccounts);
  }
}
