<template>
  <div class="license-test">
    <PageHeader :title="t`اختبار نظام الترخيص`" />
    
    <div class="p-6 space-y-6">
      <!-- معلومات الجهاز الحالي -->
      <div class="content-card p-6">
        <h3 class="text-lg font-semibold mb-4">{{ t`معلومات الجهاز الحالي` }}</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="info-item">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              {{ t`الرقم التسلسلي للجهاز` }}
            </label>
            <div class="flex">
              <input 
                type="text" 
                :value="currentSerial" 
                readonly 
                class="flex-1 p-3 border border-gray-300 rounded-l-md bg-gray-50 font-mono text-sm"
              />
              <button 
                @click="copySerial" 
                class="px-4 py-3 bg-blue-500 text-white rounded-r-md hover:bg-blue-600 transition-colors"
              >
                📋 نسخ
              </button>
            </div>
          </div>
          
          <div class="info-item">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              {{ t`حالة الترخيص الحالي` }}
            </label>
            <div class="p-3 border border-gray-300 rounded-md bg-gray-50">
              <span :class="licenseStatusClass">{{ licenseStatusText }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- اختبار إنشاء المفاتيح -->
      <div class="content-card p-6">
        <h3 class="text-lg font-semibold mb-4">{{ t`اختبار إنشاء المفاتيح` }}</h3>
        
        <div class="space-y-4">
          <button 
            @click="testKeyGeneration" 
            :disabled="isLoading"
            class="btn btn-primary"
          >
            <span v-if="isLoading">⏳ جاري الاختبار...</span>
            <span v-else>🧪 اختبار إنشاء المفاتيح</span>
          </button>
          
          <div v-if="testResults" class="space-y-4">
            <div class="bg-green-50 border border-green-200 rounded-md p-4">
              <h4 class="font-semibold text-green-800 mb-2">{{ t`نتائج الاختبار` }}</h4>
              
              <div class="space-y-3">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ t`مفتاح تجريبي مُنشأ` }}
                  </label>
                  <div class="flex">
                    <input 
                      type="text" 
                      :value="testResults.trialKey" 
                      readonly 
                      class="flex-1 p-2 border border-gray-300 rounded-l-md bg-white font-mono text-sm"
                    />
                    <button 
                      @click="copyKey(testResults.trialKey)" 
                      class="px-3 py-2 bg-green-500 text-white rounded-r-md hover:bg-green-600 transition-colors text-sm"
                    >
                      📋
                    </button>
                  </div>
                  <div class="mt-1 text-sm">
                    <span :class="testResults.testResults.trialValid ? 'text-green-600' : 'text-red-600'">
                      {{ testResults.testResults.trialValid ? '✅ صحيح' : '❌ خاطئ' }}
                    </span>
                  </div>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    {{ t`مفتاح دائم مُنشأ` }}
                  </label>
                  <div class="flex">
                    <input 
                      type="text" 
                      :value="testResults.permanentKey" 
                      readonly 
                      class="flex-1 p-2 border border-gray-300 rounded-l-md bg-white font-mono text-sm"
                    />
                    <button 
                      @click="copyKey(testResults.permanentKey)" 
                      class="px-3 py-2 bg-green-500 text-white rounded-r-md hover:bg-green-600 transition-colors text-sm"
                    >
                      📋
                    </button>
                  </div>
                  <div class="mt-1 text-sm">
                    <span :class="testResults.testResults.permanentValid ? 'text-green-600' : 'text-red-600'">
                      {{ testResults.testResults.permanentValid ? '✅ صحيح' : '❌ خاطئ' }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- اختبار مفتاح مخصص -->
      <div class="content-card p-6">
        <h3 class="text-lg font-semibold mb-4">{{ t`اختبار مفتاح مخصص` }}</h3>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              {{ t`أدخل مفتاح الترخيص للاختبار` }}
            </label>
            <input 
              type="text" 
              v-model="customKey" 
              placeholder="PRM-XXXX-XXXX-XXXX-XXXX"
              class="w-full p-3 border border-gray-300 rounded-md font-mono text-sm"
            />
          </div>
          
          <button 
            @click="testCustomKey" 
            :disabled="!customKey || isLoading"
            class="btn btn-secondary"
          >
            🔍 اختبار المفتاح
          </button>
          
          <div v-if="customKeyResult" class="p-4 rounded-md" :class="customKeyResult.isValid ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'">
            <div class="flex items-center space-x-2">
              <span :class="customKeyResult.isValid ? 'text-green-600' : 'text-red-600'">
                {{ customKeyResult.isValid ? '✅' : '❌' }}
              </span>
              <span class="font-medium">
                {{ customKeyResult.isValid ? 'مفتاح صحيح' : 'مفتاح خاطئ' }}
              </span>
            </div>
            
            <div v-if="customKeyResult.licenseType" class="mt-2 text-sm text-gray-600">
              نوع الترخيص: {{ customKeyResult.licenseType === 'trial' ? 'تجريبي' : 'دائم' }}
            </div>
            
            <div v-if="customKeyResult.error" class="mt-2 text-sm text-red-600">
              {{ customKeyResult.error }}
            </div>
          </div>
        </div>
      </div>

      <!-- معلومات تقنية -->
      <div class="content-card p-6">
        <h3 class="text-lg font-semibold mb-4">{{ t`معلومات تقنية` }}</h3>
        
        <div class="bg-gray-50 p-4 rounded-md">
          <h4 class="font-medium mb-2">{{ t`خوارزمية إنشاء المفاتيح` }}</h4>
          <ul class="text-sm text-gray-600 space-y-1">
            <li>• يتم إنشاء المفتاح بناءً على الرقم التسلسلي ونوع الترخيص</li>
            <li>• يستخدم SHA-256 لإنشاء hash فريد</li>
            <li>• لا يوجد كلمة سر سرية - الأمان يعتمد على الخوارزمية</li>
            <li>• كل مفتاح مرتبط بجهاز واحد فقط</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import PageHeader from 'src/components/PageHeader.vue';
import { License } from 'models/baseModels/License/License';
import { showToast } from 'src/utils/interactive';

export default defineComponent({
  name: 'LicenseTest',
  components: {
    PageHeader,
  },
  data() {
    return {
      currentSerial: '',
      testResults: null as any,
      customKey: '',
      customKeyResult: null as any,
      isLoading: false,
      licenseStatus: null as any,
    };
  },
  computed: {
    licenseStatusText() {
      if (!this.licenseStatus) return 'غير معروف';
      return this.licenseStatus.isValid ? 'نشط' : 'غير نشط';
    },
    licenseStatusClass() {
      if (!this.licenseStatus) return 'text-gray-600';
      return this.licenseStatus.isValid ? 'text-green-600 font-semibold' : 'text-red-600 font-semibold';
    },
  },
  async mounted() {
    await this.loadCurrentInfo();
  },
  methods: {
    async loadCurrentInfo() {
      try {
        // الحصول على الرقم التسلسلي الحالي
        this.currentSerial = License.generateSerialNumber();
        
        // فحص حالة الترخيص الحالي
        const licenseManager = await import('src/utils/licenseManager');
        this.licenseStatus = await licenseManager.getLicenseInfo();
      } catch (error) {
        console.error('Error loading current info:', error);
        showToast({
          type: 'error',
          message: 'حدث خطأ في تحميل معلومات النظام',
        });
      }
    },
    
    async testKeyGeneration() {
      this.isLoading = true;
      this.testResults = null;
      
      try {
        // اختبار إنشاء المفاتيح
        const result = License.testLicenseGeneration(this.currentSerial);
        this.testResults = result;
        
        showToast({
          type: 'success',
          message: 'تم اختبار إنشاء المفاتيح بنجاح',
        });
      } catch (error) {
        console.error('Error testing key generation:', error);
        showToast({
          type: 'error',
          message: 'حدث خطأ في اختبار إنشاء المفاتيح',
        });
      } finally {
        this.isLoading = false;
      }
    },
    
    async testCustomKey() {
      if (!this.customKey) return;
      
      this.isLoading = true;
      this.customKeyResult = null;
      
      try {
        const result = License.validateLicenseKeyWithSerial(this.customKey, this.currentSerial);
        this.customKeyResult = result;
        
        showToast({
          type: result.isValid ? 'success' : 'warning',
          message: result.isValid ? 'المفتاح صحيح' : 'المفتاح غير صحيح',
        });
      } catch (error) {
        console.error('Error testing custom key:', error);
        showToast({
          type: 'error',
          message: 'حدث خطأ في اختبار المفتاح',
        });
      } finally {
        this.isLoading = false;
      }
    },
    
    async copySerial() {
      try {
        await navigator.clipboard.writeText(this.currentSerial);
        showToast({
          type: 'success',
          message: 'تم نسخ الرقم التسلسلي',
        });
      } catch (error) {
        console.error('Error copying serial:', error);
        showToast({
          type: 'error',
          message: 'فشل في نسخ الرقم التسلسلي',
        });
      }
    },
    
    async copyKey(key: string) {
      try {
        await navigator.clipboard.writeText(key);
        showToast({
          type: 'success',
          message: 'تم نسخ المفتاح',
        });
      } catch (error) {
        console.error('Error copying key:', error);
        showToast({
          type: 'error',
          message: 'فشل في نسخ المفتاح',
        });
      }
    },
  },
});
</script>

<style scoped>
.info-item {
  @apply space-y-2;
}

.btn {
  @apply px-4 py-2 rounded-md font-medium transition-colors;
}

.btn-primary {
  @apply bg-blue-500 text-white hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply bg-gray-500 text-white hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed;
}

.content-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200;
}
</style>
