# طريقة إلغاء أي عملية - دليل الإرجاع للوضع الأصلي

## 📋 فهرس المحتويات
1. إلغاء الثيم والعودة للوضع الأصلي
2. تعديل الثيم الحالي
3. إظهار المديولات في الشريط الجانبي
4. تعديل المديولات الموجودة
5. إلغاء التحديثات والعودة لإصدار سابق
6. استكشاف الأخطاء وحلها

---

## 🎨 1. إلغاء الثيم والعودة للوضع الأصلي

### **الطريقة الأولى: من الواجهة**
```
1. انقر على زر الثيم في أسفل الشريط الجانبي
2. اختر "Modern Blue" (الثيم الأصلي)
3. سيتم تطبيق الثيم الأصلي فوراً
```

### **الطريقة الثانية: حذف الثيم المحفوظ**
```javascript
// في وحدة تحكم المتصفح (F12)
localStorage.removeItem('selectedTheme');
window.location.reload();
```

### **الطريقة الثالثة: تعديل الكود**
```typescript
// في src/utils/theme.ts
// غير السطر:
let currentTheme: string = 'skyBlue';
// إلى:
let currentTheme: string = 'modern';

// وغير السطر:
const savedTheme = localStorage.getItem('selectedTheme') || 'skyBlue';
// إلى:
const savedTheme = localStorage.getItem('selectedTheme') || 'modern';
```

### **الطريقة الرابعة: إزالة الثيم نهائياً**
```bash
# احذف ملفات الثيم
rm src/styles/theme.css
rm src/components/ThemeSelector.vue

# أزل الاستيراد من index.css
# احذف السطر: @import './theme.css';
```

---

## ✏️ 2. تعديل الثيم الحالي

### **تغيير ألوان الثيم السماوي:**
```css
/* في src/styles/theme.css */
[data-theme="skyBlue"] {
  --sidebar-bg: #075985;        /* غير هذا اللون */
  --sidebar-text: #F0F9FF;      /* غير هذا اللون */
  --content-bg: #F0F9FF;        /* غير هذا اللون */
  --text-primary: #075985;      /* غير هذا اللون */
  --text-accent: #0EA5E9;       /* غير هذا اللون */
}
```

### **إضافة ثيم جديد:**
```typescript
// في src/utils/theme.ts
myCustom: {
  name: 'الثيم المخصص',
  colors: {
    sidebar: 'bg-purple-800',
    content: 'bg-purple-50',
    primary: 'bg-purple-600',
    secondary: 'bg-purple-500',
    accent: 'bg-pink-500'
  },
  darkMode: false
},
```

```css
/* في src/styles/theme.css */
[data-theme="myCustom"] {
  --sidebar-bg: #6B46C1;
  --sidebar-text: #F3E8FF;
  --content-bg: #FAF5FF;
  --text-primary: #6B46C1;
  --text-accent: #EC4899;
}
```

### **تعديل خطوط الثيم:**
```css
/* في src/styles/theme.css */
.theme-text-primary {
  color: var(--text-primary) !important;
  font-weight: 700; /* غير وزن الخط */
  font-size: 1.1em; /* غير حجم الخط */
}
```

---

## 👁️ 3. إظهار المديولات في الشريط الجانبي

### **إظهار معلومات الترخيص:**
```typescript
// في src/utils/sidebarConfig.ts
{
  label: t`معلومات الترخيص`,
  name: 'license-info',
  route: '/license-info',
  hidden: () => {
    // غير من true إلى false لإظهار المديول
    return false;
  },
},
```

### **إظهار مولد التراخيص:**
```typescript
{
  label: t`مولد التراخيص`,
  name: 'license-generator',
  route: '/license-generator',
  hidden: () => {
    // إظهار للجميع
    return false;
    
    // أو إظهار للمدير فقط
    const currentUser = localStorage.getItem('currentUser');
    return currentUser !== 'admin';
  },
},
```

### **إضافة مديول جديد:**
```typescript
// في src/utils/sidebarConfig.ts
{
  label: t`المديول الجديد`,
  name: 'new-module',
  route: '/new-module',
  icon: 'grid', // اختر أيقونة من Feather Icons
  hidden: () => false, // مرئي للجميع
},
```

### **إضافة مديول بصفحات فرعية:**
```typescript
{
  label: t`المديول الرئيسي`,
  name: 'main-module',
  route: '/main-module',
  icon: 'folder',
  items: [
    {
      label: t`الصفحة الفرعية 1`,
      name: 'sub-page-1',
      route: '/main-module/sub-page-1',
    },
    {
      label: t`الصفحة الفرعية 2`,
      name: 'sub-page-2',
      route: '/main-module/sub-page-2',
    },
  ],
  hidden: () => false,
},
```

---

## 🔧 4. تعديل المديولات الموجودة

### **تغيير اسم مديول:**
```typescript
// في src/utils/sidebarConfig.ts
{
  label: t`الاسم الجديد`, // غير هذا
  name: 'sales',
  route: '/sales',
  // باقي الإعدادات...
},
```

### **تغيير أيقونة مديول:**
```typescript
{
  label: t`المبيعات`,
  name: 'sales',
  route: '/sales',
  icon: 'shopping-cart', // غير الأيقونة
  // باقي الإعدادات...
},
```

### **إخفاء مديول موجود:**
```typescript
{
  label: t`المبيعات`,
  name: 'sales',
  route: '/sales',
  hidden: () => true, // غير إلى true لإخفاء المديول
},
```

### **إضافة شروط إظهار:**
```typescript
{
  label: t`المبيعات`,
  name: 'sales',
  route: '/sales',
  hidden: () => {
    // إظهار للمدير فقط
    const userRole = localStorage.getItem('userRole');
    return userRole !== 'Admin';
    
    // أو إظهار لمستخدمين محددين
    const currentUser = localStorage.getItem('currentUser');
    const allowedUsers = ['admin', 'sales_manager'];
    return !allowedUsers.includes(currentUser);
    
    // أو إظهار حسب الصلاحيات
    const permissions = JSON.parse(localStorage.getItem('userPermissions') || '[]');
    return !permissions.includes('sales_access');
  },
},
```

---

## ⏪ 5. إلغاء التحديثات والعودة لإصدار سابق

### **باستخدام Git:**
```bash
# عرض التغييرات الأخيرة
git log --oneline -10

# العودة لآخر commit
git reset --hard HEAD~1

# العودة لcommit محدد
git reset --hard COMMIT_HASH

# إلغاء تغييرات ملف محدد
git checkout HEAD -- src/utils/theme.ts
```

### **استعادة ملف من نسخة احتياطية:**
```bash
# إذا كان لديك نسخة احتياطية
cp backup/src/utils/theme.ts src/utils/theme.ts
cp backup/src/styles/index.css src/styles/index.css
```

### **حذف ملفات جديدة:**
```bash
# احذف الملفات المضافة
rm src/styles/theme.css
rm src/components/ThemeSelector.vue
rm src/pages/SkyBlueDemo.vue
rm "الثيم السماوي الهادئ - دليل التحسينات.txt"
```

---

## 🔄 6. إعادة تعيين كاملة للنظام

### **إعادة تعيين الثيم:**
```typescript
// في src/utils/theme.ts
// احذف كل المحتوى واستبدله بـ:
export function setDarkMode(darkMode: boolean): void {
  if (darkMode) {
    document.documentElement.classList.add('dark');
    return;
  }
  document.documentElement.classList.remove('dark');
}
```

### **إعادة تعيين الشريط الجانبي:**
```typescript
// في src/utils/sidebarConfig.ts
// أزل جميع المديولات المخفية وأعد hidden إلى false
hidden: () => false,
```

### **إعادة تعيين الألوان:**
```bash
# استعد ملف الألوان الأصلي
git checkout HEAD -- colors.json
```

### **إعادة تعيين CSS:**
```css
/* في src/styles/index.css */
/* احذف السطر: */
@import './theme.css';
```

---

## 🛠️ 7. أمثلة عملية للتعديل

### **مثال 1: إظهار مديول المخزون للجميع**
```typescript
// في src/utils/sidebarConfig.ts
// ابحث عن مديول Inventory وغير:
hidden: () => {
  // الكود الحالي المعقد
},
// إلى:
hidden: () => false,
```

### **مثال 2: تغيير لون الثيم السماوي إلى أخضر**
```css
/* في src/styles/theme.css */
[data-theme="skyBlue"] {
  --sidebar-bg: #166534;        /* أخضر بدلاً من أزرق */
  --sidebar-text: #DCFCE7;      /* أخضر فاتح */
  --content-bg: #F0FDF4;        /* أخضر فاتح جداً */
  --text-primary: #166534;      /* أخضر داكن */
  --text-accent: #22C55E;       /* أخضر مشرق */
}
```

### **مثال 3: إضافة مديول إدارة المخازن**
```typescript
// في src/utils/sidebarConfig.ts
{
  label: t`إدارة المخازن`,
  name: 'warehouse-management',
  route: '/warehouse',
  icon: 'package',
  items: [
    {
      label: t`المخازن`,
      name: 'warehouses',
      route: '/warehouse/list',
    },
    {
      label: t`نقل البضائع`,
      name: 'transfers',
      route: '/warehouse/transfers',
    },
  ],
  hidden: () => {
    const userRole = localStorage.getItem('userRole');
    return !['Admin', 'Warehouse_Manager'].includes(userRole);
  },
},
```

---

## 🚨 8. استكشاف الأخطاء وحلها

### **خطأ: الثيم لا يتغير**
```bash
# الحلول:
1. امسح cache المتصفح (Ctrl+Shift+Delete)
2. أعد تشغيل الخادم: npm run dev
3. تحقق من وحدة التحكم للأخطاء (F12)
4. احذف localStorage: localStorage.clear()
```

### **خطأ: المديول لا يظهر**
```typescript
// تحقق من:
1. hidden: () => false  // تأكد أنه false
2. المسار موجود في router.ts
3. المكون موجود في المجلد الصحيح
4. لا توجد أخطاء في الكونسول
```

### **خطأ: CSS لا يعمل**
```bash
# الحلول:
1. تحقق من استيراد الملف في index.css
2. تأكد من صحة مسار الملف
3. أعد تشغيل الخادم
4. تحقق من أخطاء PostCSS في الكونسول
```

---

## 📝 9. قائمة مرجعية للتراجع

### **تراجع سريع (5 دقائق):**
- [ ] حذف localStorage: `localStorage.clear()`
- [ ] إعادة تشغيل الخادم: `npm run dev`
- [ ] تغيير الثيم من الواجهة

### **تراجع متوسط (15 دقيقة):**
- [ ] استعادة ملفات من Git: `git checkout HEAD -- filename`
- [ ] حذف ملفات جديدة غير مرغوبة
- [ ] تعديل إعدادات sidebarConfig.ts

### **تراجع كامل (30 دقيقة):**
- [ ] `git reset --hard HEAD~5` (العودة 5 commits)
- [ ] حذف جميع الملفات الجديدة
- [ ] إعادة تثبيت المكتبات: `npm install`
- [ ] إعادة تشغيل النظام

---

## 🔗 10. روابط مفيدة

### **ملفات مهمة للتعديل:**
- `src/utils/theme.ts` - إعدادات الثيم
- `src/utils/sidebarConfig.ts` - إعدادات القائمة الجانبية
- `src/styles/theme.css` - تصميم الثيم
- `src/router.ts` - مسارات الصفحات
- `colors.json` - ألوان النظام

### **أوامر مفيدة:**
```bash
# إعادة تشغيل الخادم
npm run dev

# فحص الأخطاء
npm run lint

# بناء النظام
npm run build

# عرض حالة Git
git status

# عرض التغييرات
git diff
```

---

## ⚠️ تحذيرات مهمة

1. **احتفظ بنسخة احتياطية** قبل أي تعديل كبير
2. **اختبر التغييرات** في بيئة التطوير أولاً
3. **لا تحذف ملفات** إلا إذا كنت متأكداً
4. **استخدم Git** لحفظ التغييرات المهمة
5. **اقرأ رسائل الأخطاء** في وحدة التحكم

---

**هذا الدليل يساعدك في إلغاء أي تعديل والعودة للوضع الأصلي بسهولة!** 🔄✨

---

## 📚 11. أمثلة متقدمة للتخصيص

### **إنشاء ثيم مخصص كامل:**
```typescript
// 1. في src/utils/theme.ts
corporateBlue: {
  name: 'الثيم المؤسسي الأزرق',
  colors: {
    sidebar: 'bg-blue-900',
    content: 'bg-blue-25',
    primary: 'bg-blue-700',
    secondary: 'bg-blue-600',
    accent: 'bg-indigo-600'
  },
  darkMode: false
},
```

```css
/* 2. في src/styles/theme.css */
[data-theme="corporateBlue"] {
  --sidebar-bg: #1E3A8A;
  --sidebar-text: #DBEAFE;
  --sidebar-hover: #1D4ED8;
  --sidebar-active: #6366F1;
  --content-bg: #F0F9FF;
  --content-secondary: #DBEAFE;
  --content-border: #93C5FD;
  --text-primary: #1E3A8A;
  --text-secondary: #1D4ED8;
  --text-accent: #6366F1;
  --text-muted: #6B7280;
}
```

```typescript
// 3. في src/components/ThemeSelector.vue
corporateBlue: 'ثيم مؤسسي أزرق احترافي',
```

### **إنشاء مديول متكامل:**
```typescript
// 1. إنشاء الصفحة: src/pages/CustomModule.vue
<template>
  <div class="custom-module">
    <PageHeader :title="t`المديول المخصص`" />
    <div class="dashboard-card">
      <h2>محتوى المديول المخصص</h2>
      <!-- المحتوى هنا -->
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import PageHeader from 'src/components/PageHeader.vue';

export default defineComponent({
  name: 'CustomModule',
  components: { PageHeader },
});
</script>
```

```typescript
// 2. إضافة المسار في src/router.ts
import CustomModule from 'src/pages/CustomModule.vue';

{
  path: '/custom-module',
  name: 'Custom Module',
  component: CustomModule,
  meta: { requiresAuth: true }
},
```

```typescript
// 3. إضافة في الشريط الجانبي src/utils/sidebarConfig.ts
{
  label: t`المديول المخصص`,
  name: 'custom-module',
  route: '/custom-module',
  icon: 'star',
  hidden: () => false,
},
```

---

## 🔧 12. تخصيص متقدم للصلاحيات

### **إنشاء نظام صلاحيات مخصص:**
```typescript
// في src/utils/permissions.ts (ملف جديد)
export interface UserPermissions {
  canViewSales: boolean;
  canEditSales: boolean;
  canViewInventory: boolean;
  canEditInventory: boolean;
  canViewReports: boolean;
  canManageUsers: boolean;
  canChangeTheme: boolean;
}

export function getUserPermissions(): UserPermissions {
  const userRole = localStorage.getItem('userRole');
  const currentUser = localStorage.getItem('currentUser');

  // صلاحيات المدير
  if (currentUser === 'admin' || userRole === 'Admin') {
    return {
      canViewSales: true,
      canEditSales: true,
      canViewInventory: true,
      canEditInventory: true,
      canViewReports: true,
      canManageUsers: true,
      canChangeTheme: true,
    };
  }

  // صلاحيات مدير المبيعات
  if (userRole === 'Sales_Manager') {
    return {
      canViewSales: true,
      canEditSales: true,
      canViewInventory: true,
      canEditInventory: false,
      canViewReports: true,
      canManageUsers: false,
      canChangeTheme: true,
    };
  }

  // صلاحيات المستخدم العادي
  return {
    canViewSales: false,
    canEditSales: false,
    canViewInventory: false,
    canEditInventory: false,
    canViewReports: false,
    canManageUsers: false,
    canChangeTheme: false,
  };
}
```

### **استخدام الصلاحيات في الشريط الجانبي:**
```typescript
// في src/utils/sidebarConfig.ts
import { getUserPermissions } from 'src/utils/permissions';

{
  label: t`المبيعات`,
  name: 'sales',
  route: '/sales',
  hidden: () => {
    const permissions = getUserPermissions();
    return !permissions.canViewSales;
  },
},
```

---

## 🎨 13. تخصيص الألوان والخطوط

### **إضافة خطوط مخصصة:**
```css
/* في src/styles/theme.css */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

[data-theme="skyBlue"] {
  --font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
}

body {
  font-family: var(--font-family);
}

.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
```

### **إضافة متغيرات ألوان مخصصة:**
```css
[data-theme="skyBlue"] {
  /* ألوان الحالة */
  --color-success: #10B981;
  --color-warning: #F59E0B;
  --color-error: #EF4444;
  --color-info: #3B82F6;

  /* ألوان الخلفيات */
  --bg-success: #ECFDF5;
  --bg-warning: #FFFBEB;
  --bg-error: #FEF2F2;
  --bg-info: #EFF6FF;

  /* ألوان الحدود */
  --border-success: #A7F3D0;
  --border-warning: #FDE68A;
  --border-error: #FECACA;
  --border-info: #BFDBFE;
}
```

---

## 🔄 14. نسخ احتياطية وإستعادة

### **إنشاء نسخة احتياطية:**
```bash
# إنشاء مجلد النسخ الاحتياطية
mkdir backup_$(date +%Y%m%d_%H%M%S)

# نسخ الملفات المهمة
cp -r src/utils backup_*/
cp -r src/styles backup_*/
cp -r src/components backup_*/
cp colors.json backup_*/

# أو استخدام Git
git add .
git commit -m "نسخة احتياطية قبل التعديل - $(date)"
git tag backup_$(date +%Y%m%d_%H%M%S)
```

### **استعادة من نسخة احتياطية:**
```bash
# استعادة من مجلد
cp -r backup_20241216_180000/src/utils/* src/utils/
cp -r backup_20241216_180000/src/styles/* src/styles/

# أو استعادة من Git tag
git checkout backup_20241216_180000

# أو استعادة ملف محدد
git checkout backup_20241216_180000 -- src/utils/theme.ts
```

---

## 🛡️ 15. الحماية من الأخطاء

### **التحقق من صحة التعديلات:**
```typescript
// في src/utils/validation.ts (ملف جديد)
export function validateThemeConfig(theme: any): boolean {
  const requiredFields = ['name', 'colors'];
  const requiredColors = ['sidebar', 'content', 'primary'];

  if (!requiredFields.every(field => theme[field])) {
    console.error('Theme missing required fields');
    return false;
  }

  if (!requiredColors.every(color => theme.colors[color])) {
    console.error('Theme missing required colors');
    return false;
  }

  return true;
}

export function validateSidebarItem(item: any): boolean {
  const requiredFields = ['label', 'name', 'route'];

  if (!requiredFields.every(field => item[field])) {
    console.error('Sidebar item missing required fields');
    return false;
  }

  return true;
}
```

### **معالجة الأخطاء:**
```typescript
// في src/utils/errorHandler.ts (ملف جديد)
export function handleThemeError(error: Error): void {
  console.error('Theme Error:', error);

  // العودة للثيم الافتراضي
  localStorage.setItem('selectedTheme', 'modern');
  window.location.reload();
}

export function handleSidebarError(error: Error): void {
  console.error('Sidebar Error:', error);

  // إخفاء العنصر المعطل
  const errorElement = document.querySelector('[data-error="true"]');
  if (errorElement) {
    errorElement.style.display = 'none';
  }
}
```

---

## 📊 16. مراقبة الأداء

### **قياس أداء الثيم:**
```typescript
// في src/utils/performance.ts (ملف جديد)
export function measureThemePerformance(): void {
  const start = performance.now();

  // تطبيق الثيم
  document.documentElement.setAttribute('data-theme', 'skyBlue');

  const end = performance.now();
  console.log(`Theme applied in ${end - start} milliseconds`);
}

export function monitorMemoryUsage(): void {
  if ('memory' in performance) {
    const memory = (performance as any).memory;
    console.log('Memory usage:', {
      used: Math.round(memory.usedJSHeapSize / 1048576) + ' MB',
      total: Math.round(memory.totalJSHeapSize / 1048576) + ' MB',
      limit: Math.round(memory.jsHeapSizeLimit / 1048576) + ' MB'
    });
  }
}
```

---

## 🔍 17. أدوات التشخيص

### **فحص حالة النظام:**
```typescript
// في src/utils/diagnostics.ts (ملف جديد)
export function systemDiagnostics(): void {
  console.log('=== تشخيص النظام ===');

  // فحص الثيم الحالي
  const currentTheme = localStorage.getItem('selectedTheme');
  console.log('الثيم الحالي:', currentTheme);

  // فحص المستخدم الحالي
  const currentUser = localStorage.getItem('currentUser');
  const userRole = localStorage.getItem('userRole');
  console.log('المستخدم:', currentUser, 'الدور:', userRole);

  // فحص الصلاحيات
  const permissions = localStorage.getItem('userPermissions');
  console.log('الصلاحيات:', permissions);

  // فحص الأخطاء في الكونسول
  const errors = document.querySelectorAll('.error');
  console.log('عدد الأخطاء المرئية:', errors.length);

  // فحص حالة الاتصال
  console.log('حالة الاتصال:', navigator.onLine ? 'متصل' : 'غير متصل');
}

// تشغيل التشخيص
// systemDiagnostics();
```

### **إعادة تعيين كاملة للنظام:**
```typescript
export function fullSystemReset(): void {
  if (confirm('هل أنت متأكد من إعادة تعيين النظام بالكامل؟')) {
    // مسح جميع البيانات المحلية
    localStorage.clear();
    sessionStorage.clear();

    // إعادة تحميل الصفحة
    window.location.href = '/';
  }
}
```

---

## 📋 18. قائمة مرجعية شاملة

### **قبل أي تعديل:**
- [ ] إنشاء نسخة احتياطية
- [ ] فهم التعديل المطلوب
- [ ] التأكد من وجود طريقة للتراجع
- [ ] اختبار في بيئة التطوير

### **أثناء التعديل:**
- [ ] تعديل ملف واحد في كل مرة
- [ ] اختبار كل تعديل فوراً
- [ ] مراقبة رسائل الأخطاء
- [ ] حفظ التقدم بانتظام

### **بعد التعديل:**
- [ ] اختبار جميع الوظائف
- [ ] التأكد من عدم وجود أخطاء
- [ ] توثيق التغييرات
- [ ] إنشاء commit في Git

### **في حالة المشاكل:**
- [ ] فحص وحدة التحكم للأخطاء
- [ ] التراجع للنسخة السابقة
- [ ] إعادة تشغيل الخادم
- [ ] مسح cache المتصفح

---

**هذا الدليل الشامل يغطي جميع جوانب إلغاء العمليات والتعديل على النظام!** 🔄📚✨

**آخر تحديث:** 2024-12-16
**الإصدار:** 2.0.0 (محدث ومحسن)
